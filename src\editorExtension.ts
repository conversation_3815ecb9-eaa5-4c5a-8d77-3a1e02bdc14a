import { Extension, RangeSetBuilder } from '@codemirror/state';
import { Decoration, DecorationSet, EditorView, ViewPlugin, ViewUpdate } from '@codemirror/view';
import { syntaxTree } from '@codemirror/language';
import MyPlugin from '../main';

// 全局前缀映射，存储隐藏的前缀信息
declare global {
    interface Window {
        crossVaultPrefixMap: Map<string, string>; // key: 显示的文件名, value: 完整的链接
    }
}

// 初始化全局前缀映射
if (!window.crossVaultPrefixMap) {
    window.crossVaultPrefixMap = new Map();
}

// 借鉴参考插件的 iterateTree 函数
interface IterateOptions {
    tree: any;
    range: any;
    enter(cursor: any): void;
}

const iterateTree = ({ tree, range, enter }: IterateOptions): void => {
    const cursor = tree.cursor();
    do {
        if (intersectRange(range, cursor)) {
            enter(cursor);
            if (cursor.firstChild()) continue;
        }

        while (!cursor.nextSibling()) {
            if (!cursor.parent()) return;
        }
    } while (true);
};

const intersectRange = (a: any, b: any): boolean => a.from <= b.to && a.to >= b.from;

// 创建真正的 CodeMirror 装饰系统
export function createEditorExtension(plugin: MyPlugin): Extension {
    // console.log('[Connect Vault] 开始创建 CodeMirror 装饰扩展');
    // console.log('[Connect Vault] 插件设置:', plugin.settings);
    // console.log('[Connect Vault] 连接数量:', plugin.settings.connections.length);

    const viewPlugin = ViewPlugin.fromClass(
        class {
            decorations: DecorationSet;

            constructor(view: EditorView) {
                // console.log('[Connect Vault] ViewPlugin 构造函数被调用');
                // console.log('[Connect Vault] EditorView:', view);
                this.decorations = this.buildDecorations(view);
            }

            update(update: ViewUpdate) {
                // console.log('[Connect Vault] ViewPlugin update 被调用');
                // console.log('[Connect Vault] 更新原因 - docChanged:', update.docChanged, 'viewportChanged:', update.viewportChanged, 'selectionSet:', update.selectionSet);

                if (update.docChanged || update.viewportChanged || update.selectionSet) {
                    // console.log('[Connect Vault] 重新构建装饰');
                    this.decorations = this.buildDecorations(update.view);
                } else {
                    // console.log('[Connect Vault] 跳过装饰重建');
                }
            }

            buildDecorations(view: EditorView): DecorationSet {
                // console.log('[Connect Vault] === buildDecorations 开始 ===');

                const builder = new RangeSetBuilder<Decoration>();
                const selection = view.state.selection.main;

                // console.log('[Connect Vault] 选择范围:', selection.from, '-', selection.to);

                // 使用语法树遍历，借鉴参考插件的方法
                const tree = syntaxTree(view.state);
                // console.log('[Connect Vault] 语法树:', tree);

                for (const range of view.visibleRanges) {
                    // console.log('[Connect Vault] 处理可见范围:', range.from, '-', range.to);

                    iterateTree({
                        tree,
                        range,
                        enter: (cursor) => {
                            // console.log('[Connect Vault] 语法节点:', cursor.name, cursor.from, '-', cursor.to);

                            // 查找内部链接的开始标记 [[
                            if (cursor.name.contains("formatting-link-start") && !cursor.name.contains("footref")) {
                                // console.log('[Connect Vault] 找到链接开始标记');
                                this.processInternalLink(cursor, view, builder, selection, plugin);
                            }
                        }
                    });
                }

                const decorationSet = builder.finish();
                // console.log('[Connect Vault] 装饰构建完成，装饰数量:', decorationSet.size);
                // console.log('[Connect Vault] === buildDecorations 结束 ===');

                return decorationSet;
            }

            processInternalLink(cursor: any, view: EditorView, builder: RangeSetBuilder<Decoration>, selection: any, plugin: MyPlugin) {
                // console.log('[Connect Vault] === processInternalLink 开始 ===');

                // 借鉴参考插件的方法
                const startFrom = cursor.from;
                const linkFrom = cursor.to;

                // console.log(`[Connect Vault] 链接开始位置: ${startFrom}, 内容开始位置: ${linkFrom}`);

                // 找到链接结束标记 ]]
                while (cursor.nextSibling() && !cursor.name.contains("formatting-link-end")) {
                    // console.log(`[Connect Vault] 跳过节点: ${cursor.name}`);
                }

                if (!cursor.name.contains("formatting-link-end")) {
                    // console.log('[Connect Vault] 未找到链接结束标记');
                    return;
                }

                const linkTo = cursor.from;
                const endTo = cursor.to;

                // console.log(`[Connect Vault] 内容结束位置: ${linkTo}, 链接结束位置: ${endTo}`);

                // 获取链接内容
                const linkContent = view.state.sliceDoc(linkFrom, linkTo);
                // console.log(`[Connect Vault] 链接内容: "${linkContent}"`);

                // 检查是否是跨库链接
                const match = linkContent.match(/^(.*?):(.*?)$/);
                if (!match) {
                    // console.log('[Connect Vault] 不是跨库链接格式');
                    return;
                }

                const [_, prefix, filename] = match;
                // console.log(`[Connect Vault] 跨库链接 - 前缀: "${prefix}", 文件名: "${filename}"`);

                // 存储前缀映射信息
                window.crossVaultPrefixMap.set(filename, linkContent);
                // console.log(`[Connect Vault] 存储前缀映射: "${filename}" -> "${linkContent}"`);

                // 检查连接配置
                const connection = plugin.settings.connections.find(conn =>
                    conn.description === prefix
                );

                if (!connection) {
                    // console.log(`[Connect Vault] 未找到前缀 "${prefix}" 的连接配置`);
                    return;
                }

                // 检查是否被选中
                const intersectWithLink = (range: any) =>
                    intersectRange({ from: startFrom, to: endTo }, range);
                const isSelected = view.state.selection.ranges.some(intersectWithLink);

                // console.log(`[Connect Vault] 链接是否被选中: ${isSelected}`);

                // 如果没有被选中，隐藏前缀
                if (!isSelected) {
                    const prefixStart = linkFrom;
                    const prefixEnd = linkFrom + prefix.length + 1; // 包括 :

                    // console.log(`[Connect Vault] 隐藏前缀范围: ${prefixStart}-${prefixEnd}`);

                    try {
                        builder.add(prefixStart, prefixEnd, Decoration.replace({}));
                        // console.log(`[Connect Vault] 装饰添加成功`);

                        // 移除自定义预览功能，让 Obsidian 内置预览接管

                    } catch (error) {
                        console.error(`[Connect Vault] 添加装饰时出错:`, error);
                    }
                } else {
                    // console.log(`[Connect Vault] 链接被选中，显示完整内容`);
                }

                // console.log('[Connect Vault] === processInternalLink 结束 ===');
            }
        },
        {
            decorations: (v) => v.decorations,
        }
    );

    // console.log('[Connect Vault] ViewPlugin 创建完成:', viewPlugin);
    // console.log('[Connect Vault] === createEditorExtension 结束 ===');

    return viewPlugin;
}
