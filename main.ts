import { Plugin, Notice, MarkdownView } from 'obsidian';
import * as http from 'http';
import { SampleSettingTab, MyPluginSettings, DEFAULT_SETTINGS } from './src/settings';
import { initializeServer } from './src/server';
import { loadStyles } from './src/ui';
import { FilenameSuggest } from './src/ui';
import { removeUnresolved, setupGlobalKeyHandler } from './src/handlers';
import { createEditorExtension } from './src/editorExtension';
import { createMarkdownPostProcessor } from './src/markdownPostProcessor';
import { MonkeyPatchManager } from './src/monkeyPatches';
import { EventHandlerManager } from './src/eventHandlers';
import { RemoteCacheEntry } from './src/types';
import { parseCrossVaultLink } from './src/linkParser';

export default class MyPlugin extends Plugin {
    settings: MyPluginSettings;
    server: http.Server;
    private editorExtension: any;
    private markdownPostProcessor: any;
    private remoteCacheMap: Map<string, RemoteCacheEntry> = new Map();
    private monkeyPatchManager: MonkeyPatchManager;
    private eventHandlerManager: EventHandlerManager;
    crossVaultMetadataCache: Map<string, any> = new Map();

    /**
     * 通过 Obsidian 的多 vault 功能读取跨库内容
     */
    async readCrossVaultContentDirect(prefix: string, filename: string, anchor?: string): Promise<string | null> {
        try {
            // console.log(`[Connect Vault] 尝试直接读取跨库内容: ${prefix}:${filename}${anchor ? '#' + anchor : ''}`);

            // 获取所有已打开的 vault
            const allVaults = (this.app as any).vaults?.getLoadedVaults?.();
            if (!allVaults) {
                // console.log(`[Connect Vault] 当前 Obsidian 版本不支持多 vault 功能`);
                return null;
            }

            // console.log(`[Connect Vault] 找到 ${allVaults.length} 个已加载的 vault`);

            // 遍历每个 vault 寻找目标文件
            for (const vault of allVaults) {
                const vaultName = vault.getName?.() || 'Unknown';
                // console.log(`[Connect Vault] 检查 vault: ${vaultName}`);

                // 检查 vault 名称是否匹配前缀
                if (vaultName !== prefix) {
                    continue;
                }

                // console.log(`[Connect Vault] 找到匹配的 vault: ${vaultName}`);

                // 在该 vault 中查找文件
                const targetFile = vault.getMarkdownFiles?.().find((file: any) =>
                    file.basename === filename
                );

                if (targetFile) {
                    // console.log(`[Connect Vault] 找到目标文件: ${targetFile.path}`);

                    // 读取文件内容
                    const content = await vault.read(targetFile);
                    // console.log(`[Connect Vault] 成功读取文件内容，长度: ${content.length}`);

                    // 如果有锚点，提取相应部分
                    if (anchor) {
                        if (anchor.startsWith('^')) {
                            // 块引用
                            const blockId = anchor.substring(1);
                            const blockContent = this.extractBlockContentFromText(content, blockId);
                            if (blockContent) {
                                console.log(`[Connect Vault] 提取块内容成功: ${blockId}`);
                                return blockContent;
                            } else {
                                console.log(`[Connect Vault] 未找到块: ${blockId}`);
                                return null;
                            }
                        } else {
                            // 章节引用
                            const headingContent = this.extractHeadingContentFromText(content, anchor);
                            if (headingContent) {
                                console.log(`[Connect Vault] 提取章节内容成功: ${anchor}`);
                                return headingContent;
                            } else {
                                console.log(`[Connect Vault] 未找到章节: ${anchor}`);
                                return null;
                            }
                        }
                    }

                    // 返回完整内容
                    return content;
                }
            }

            console.log(`[Connect Vault] 未在任何 vault 中找到文件: ${prefix}:${filename}`);
            return null;

        } catch (error) {
            console.error(`[Connect Vault] 直接读取跨库内容失败:`, error);
            return null;
        }
    }

    /**
     * 从文本中提取块内容
     */
    private extractBlockContentFromText(content: string, blockId: string): string | null {
        const lines = content.split('\n');
        let blockIdLineIndex = -1;

        // 查找块ID标记所在的行
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes(`^${blockId}`) || line.trim() === `^${blockId}`) {
                blockIdLineIndex = i;
                break;
            }
        }

        if (blockIdLineIndex === -1) {
            return null;
        }

        // 向上查找块的开始位置
        let blockStartIndex = 0;
        for (let i = blockIdLineIndex - 1; i >= 0; i--) {
            const line = lines[i].trim();
            if (line === '') {
                blockStartIndex = i + 1;
                break;
            }
        }

        // 提取块内容
        const blockLines = lines.slice(blockStartIndex, blockIdLineIndex);
        return blockLines.join('\n').trim();
    }

    /**
     * 从文本中提取章节内容
     */
    private extractHeadingContentFromText(content: string, heading: string): string | null {
        const lines = content.split('\n');
        let headingLineIndex = -1;
        let headingLevel = 0;

        // 查找目标标题
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const match = line.match(/^(#{1,6})\s+(.+)$/);
            if (match && match[2].trim() === heading) {
                headingLineIndex = i;
                headingLevel = match[1].length;
                break;
            }
        }

        if (headingLineIndex === -1) {
            return null;
        }

        // 查找章节结束位置
        let endIndex = lines.length;
        for (let i = headingLineIndex + 1; i < lines.length; i++) {
            const line = lines[i];
            const match = line.match(/^(#{1,6})\s+(.+)$/);
            if (match && match[1].length <= headingLevel) {
                endIndex = i;
                break;
            }
        }

        // 提取章节内容
        const sectionLines = lines.slice(headingLineIndex, endIndex);
        return sectionLines.join('\n').trim();
    }

    /**
     * 获取所有 vault 的元数据信息
     */
    getAllVaultsMetadata(): any[] {
        try {
            const allVaults = (this.app as any).vaults?.getLoadedVaults?.();
            if (!allVaults) {
                console.log(`[Connect Vault] 当前 Obsidian 版本不支持多 vault 功能`);
                return [];
            }

            const vaultsInfo: any[] = [];

            // 遍历每个 vault 并读取元数据
            for (const vault of allVaults) {
                const vaultName = vault.getName?.() || 'Unknown';
                const vaultInfo: any = {
                    name: vaultName,
                    files: []
                };

                // 获取该 vault 的文件系统
                const files = vault.getMarkdownFiles?.() || [];

                files.forEach((file: any) => {
                    if (file.extension === "md") {
                        // 获取该 vault 中的文件元数据
                        const metadata = vault.metadataCache?.getFileCache?.(file);
                        vaultInfo.files.push({
                            path: file.path,
                            basename: file.basename,
                            metadata: metadata
                        });
                    }
                });

                vaultsInfo.push(vaultInfo);
                console.log(`[Connect Vault] Vault: ${vaultName}, 文件数量: ${vaultInfo.files.length}`);
            }

            return vaultsInfo;

        } catch (error) {
            console.error(`[Connect Vault] 获取 vault 元数据失败:`, error);
            return [];
        }
    }

    async onload() {
        // console.log('[Connect Vault] 插件开始加载');

        await this.loadSettings();
        // console.log('[Connect Vault] 设置加载完成，连接数量:', this.settings.connections.length);

        await this.initializeServer();
        // console.log('[Connect Vault] 服务器初始化完成');

        this.loadStyles();
        console.log('[Connect Vault] 样式加载完成');

        // 创建编辑器扩展和后处理器
        this.editorExtension = createEditorExtension(this);
        this.markdownPostProcessor = createMarkdownPostProcessor(this);

        // 注册编辑器扩展
        this.registerEditorExtension(this.editorExtension);
        // console.log('[Connect Vault] 编辑器扩展已注册');

        // 注册 Markdown 后处理器 - 处理所有模式
        this.registerMarkdownPostProcessor((element, context) => {
            // console.log('[Connect Vault] 后处理器被调用！元素:', element.tagName, element.className);
            this.markdownPostProcessor(element, context);
        });
        // console.log('[Connect Vault] Markdown 后处理器已注册');

        // 注册编辑器变化事件，确保编辑模式下的属性及时更新
        this.registerEvent(this.app.workspace.on('editor-change', () => {
            // 延迟处理，等待 DOM 更新完成
            setTimeout(() => {
                const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
                if (activeView && activeView.containerEl) {
                    // console.log('[Connect Vault] 编辑器变化，重新处理链接属性');
                    this.markdownPostProcessor(activeView.containerEl, {});
                }
            }, 100);
        }));

        // 设置全局键盘事件处理
        setupGlobalKeyHandler();

        // 初始化管理器
        this.monkeyPatchManager = new MonkeyPatchManager(this, this.settings, this.remoteCacheMap);
        this.eventHandlerManager = new EventHandlerManager(this, this.settings);

        // 设置管理器之间的引用
        this.monkeyPatchManager.setEventHandlerManager(this.eventHandlerManager);

        // 注册 Monkey Patches 和事件处理器
        this.monkeyPatchManager.registerAll();
        this.eventHandlerManager.registerAll();

        // 注册文件名建议功能
        this.registerEditorSuggest(new FilenameSuggest(this.app, this));

        // 添加设置标签
        this.addSettingTab(new SampleSettingTab(this.app, this));

        // 注册插件接口，供其他插件使用
        this.registerPluginInterface();

        // 初始化完成后，立即处理现有的链接
        setTimeout(() => {
            // console.log('[Connect Vault] 初始化完成，开始处理现有链接');
            removeUnresolved(this);
        }, 1000);
    }

    /**
     * 注册插件接口，供其他插件访问跨库元数据
     */
    private registerPluginInterface(): void {
        // 注册全局接口
        (this.app as any).plugins.plugins['connect-vault-interface'] = {
            // 设置跨库文件的自定义元数据
            setCrossVaultMetadata: (filePath: string, metadata: any) => {
                if (!this.crossVaultMetadataCache) {
                    this.crossVaultMetadataCache = new Map();
                }
                this.crossVaultMetadataCache.set(filePath, metadata);
                console.log(`[Connect Vault] 外部设置跨库元数据: ${filePath}`);
            },

            // 获取跨库文件的自定义元数据
            getCrossVaultMetadata: (filePath: string) => {
                const metadata = this.crossVaultMetadataCache?.get(filePath);
                console.log(`[Connect Vault] 外部获取跨库元数据: ${filePath}`, metadata ? '找到' : '未找到');
                return metadata;
            },

            // 获取所有跨库元数据
            getAllCrossVaultMetadata: () => {
                const allMetadata = Array.from(this.crossVaultMetadataCache?.entries() || []);
                console.log(`[Connect Vault] 外部获取所有跨库元数据: ${allMetadata.length} 个条目`);
                return allMetadata;
            },

            // 清除跨库元数据缓存
            clearCrossVaultMetadata: (filePath?: string) => {
                if (filePath) {
                    this.crossVaultMetadataCache?.delete(filePath);
                    console.log(`[Connect Vault] 外部清除跨库元数据: ${filePath}`);
                } else {
                    this.crossVaultMetadataCache?.clear();
                    console.log(`[Connect Vault] 外部清除所有跨库元数据`);
                }
            },

            // 直接读取跨库内容
            readCrossVaultContent: async (prefix: string, filename: string, anchor?: string) => {
                return await this.readCrossVaultContentDirect(prefix, filename, anchor);
            },

            // 获取所有 vault 信息
            getAllVaultsInfo: () => {
                return this.getAllVaultsMetadata();
            },

            // 获取跨库文件信息
            getCrossVaultFileInfo: async (prefix: string, filename: string) => {
                const connection = this.settings.connections.find(conn => conn.description === prefix);
                if (!connection) {
                    console.log(`[Connect Vault] 未找到连接配置: ${prefix}`);
                    return null;
                }

                try {
                    const infoUrl = `http://127.0.0.1:${connection.port}/api/note/info?name=${encodeURIComponent(filename)}`;
                    const response = await fetch(infoUrl);

                    if (!response.ok) {
                        console.log(`[Connect Vault] Info API 请求失败: ${response.status}`);
                        return null;
                    }

                    const data = await response.json();
                    console.log(`[Connect Vault] 获取文件信息成功: ${prefix}:${filename}`);
                    return data;
                } catch (error) {
                    console.error(`[Connect Vault] 获取文件信息失败:`, error);
                    return null;
                }
            },

            // 获取跨库文件的路径信息
            getCrossVaultPathInfo: async (prefix: string, filename: string) => {
                // 直接调用上面定义的 getCrossVaultFileInfo 方法
                const connection = this.settings.connections.find(conn => conn.description === prefix);
                if (!connection) {
                    console.log(`[Connect Vault] 未找到连接配置: ${prefix}`);
                    return null;
                }

                try {
                    const infoUrl = `http://127.0.0.1:${connection.port}/api/note/info?name=${encodeURIComponent(filename)}`;
                    const response = await fetch(infoUrl);

                    if (!response.ok) {
                        console.log(`[Connect Vault] Info API 请求失败: ${response.status}`);
                        return null;
                    }

                    const fileInfo = await response.json();
                    if (!fileInfo || !fileInfo.info) {
                        return null;
                    }

                const info = fileInfo.info;
                return {
                    // 路径信息
                    relativePath: info.relativePath,
                    absolutePath: info.absolutePath,
                    vaultPath: info.vaultPath,
                    vaultName: info.vaultName,

                    // 目录信息
                    folderPath: info.folderPath,
                    folderName: info.folderName,
                    depth: info.depth,
                    pathSegments: info.pathSegments,

                    // 文件信息
                    basename: info.basename,
                    name: info.name,
                    extension: info.extension,

                    // 统计信息
                    size: info.size,
                    created: info.created,
                    modified: info.modified,
                    exists: info.exists
                };
                } catch (error) {
                    console.error(`[Connect Vault] 获取路径信息失败:`, error);
                    return null;
                }
            }
        };

        console.log('[Connect Vault] 插件接口已注册，其他插件可通过以下方式访问:');
        console.log('  app.plugins.plugins["connect-vault-interface"].getCrossVaultMetadata(filePath)');
    }

    onunload() {
        // 恢复所有被修改的方法
        if (this.monkeyPatchManager) {
            this.monkeyPatchManager.restoreAll();
        }

        // 关闭服务器
        if (this.server) {
            this.server.close();
        }
    }

    async loadSettings() {
        this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
    }

    async saveSettings() {
        await this.saveData(this.settings);
    }

    private async initializeServer() {
        this.server = await initializeServer(this);
    }

    private loadStyles() {
        loadStyles();
    }

    // 解析跨库链接文本
    parseCrossVaultLink(linkText: string) {
        return parseCrossVaultLink(linkText);
    }

    // 内容转换方法 - 已移至 ContentTransformer，保留用于兼容性
    transformInternalLinks(content: string, prefix: string): string {
        // 使用新的转换器
        const { transformInternalLinks } = require('./src/contentTransformer');
        return transformInternalLinks(content, prefix);
    }

    // 旧的 Monkey Patches 方法 - 已移至 MonkeyPatchManager
    private registerMonkeyPatches_REMOVED() {
        // 保存原始方法
        const originalVaultCachedRead = this.app.vault.cachedRead;
        const originalGetResourcePath = this.app.vault.getResourcePath;
        const originalExists = this.app.vault.adapter.exists;
        const originalGetFileByPath = this.app.metadataCache.getFirstLinkpathDest;
        const originalGetFileCache = this.app.metadataCache.getFileCache;

        console.log('正在注册猴子补丁...');

        // 创建一个全局存储来保存完整的链接文本
        (window as any)._crossVaultLinkCache = new Map();

        // 尝试拦截多个可能的 parseLinktext 函数位置
        console.log('[Connect Vault] 检查 parseLinktext 函数...');

        // 检查多个可能的位置
        const locations = [
            { name: 'window.parseLinktext', func: (window as any).parseLinktext },
            { name: 'app.parseLinktext', func: (this.app as any).parseLinktext },
            { name: 'metadataCache.parseLinktext', func: (this.app.metadataCache as any).parseLinktext },
            { name: 'vault.parseLinktext', func: (this.app.vault as any).parseLinktext }
        ];

        locations.forEach(location => {
            console.log(`[Connect Vault] ${location.name} 函数存在:`, !!location.func);
        });

        // 尝试拦截所有可能的位置
        const createParseLinkTextInterceptor = (originalFunc: any) => {
            return (linktext: string) => {
                console.log(`[Connect Vault] 拦截到 parseLinktext 调用: "${linktext}"`);

                // 检查是否是跨库链接
                const match = linktext.match(/^(.*?):(.*?)$/);
                if (match) {
                    const [_, prefix, pathWithAnchor] = match;

                    // 解析文件路径和锚点
                    let filename = pathWithAnchor;
                    let subpath = '';

                    // 检查是否有章节锚点 #
                    const headingMatch = pathWithAnchor.match(/^(.*?)#(.*)$/);
                    if (headingMatch) {
                        filename = headingMatch[1];
                        subpath = headingMatch[2];
                    }

                    console.log(`[Connect Vault] 跨库链接解析: 前缀="${prefix}", 文件="${filename}", 子路径="${subpath}"`);

                    // 返回解析结果，保持完整的路径信息
                    return {
                        path: `${prefix}:${filename}`,
                        subpath: subpath
                    };
                }

                // 对于普通链接，使用原始方法
                return originalFunc(linktext);
            };
        };

        // 拦截所有找到的 parseLinktext 函数
        if ((window as any).parseLinktext) {
            const original = (window as any).parseLinktext;
            (window as any).parseLinktext = createParseLinkTextInterceptor(original);
            console.log('[Connect Vault] 已拦截 window.parseLinktext');
        }

        if ((this.app as any).parseLinktext) {
            const original = (this.app as any).parseLinktext;
            (this.app as any).parseLinktext = createParseLinkTextInterceptor(original);
            console.log('[Connect Vault] 已拦截 app.parseLinktext');
        }

        if ((this.app.metadataCache as any).parseLinktext) {
            const original = (this.app.metadataCache as any).parseLinktext;
            (this.app.metadataCache as any).parseLinktext = createParseLinkTextInterceptor(original);
            console.log('[Connect Vault] 已拦截 metadataCache.parseLinktext');
        }

        if (!(window as any).parseLinktext && !(this.app as any).parseLinktext && !(this.app.metadataCache as any).parseLinktext) {
            console.log('[Connect Vault] 未找到 parseLinktext 函数，将在 getFirstLinkpathDest 中处理锚点');
        }



        // 拦截 getFileCache 方法
        this.register(() => {
            this.app.metadataCache.getFileCache = originalGetFileCache;
        });
        
        this.app.metadataCache.getFileCache = (file: any) => {
            if (!file) return null;
            
            const path = typeof file === 'string' ? file : file.path;
            console.log(`拦截到 getFileCache 调用: ${path}`);
            
            // 检查是否是远程文件
            if (file.isRemoteFile || (typeof path === 'string' && path.match(/^(.*?):(.*?)$/))) {
                console.log(`为远程文件创建缓存对象: ${path}`);

                // 检查是否已经有缓存
                const cacheKey = typeof path === 'string' ? path : file.path;
                if (this.remoteCacheMap.has(cacheKey)) {
                    console.log(`使用已缓存的远程文件缓存: ${cacheKey}`);
                    return this.remoteCacheMap.get(cacheKey);
                }

                // 异步创建缓存
                this.createRemoteFileCacheAsync(file, path);

                // 返回临时缓存
                return this.createEmptyCache();
            }
            
            return originalGetFileCache.call(this.app.metadataCache, file);
        };



        // 拦截 getFirstLinkpathDest 方法，这个方法用于解析链接
        this.register(() => {
            this.app.metadataCache.getFirstLinkpathDest = originalGetFileByPath;
        });
        
        this.app.metadataCache.getFirstLinkpathDest = (linkpath: string, sourcePath: string, subpath?: string) => {
            // 只对跨库链接输出详细日志，避免日志污染
            if (linkpath.includes(':')) {
                console.log(`\n🔍 [Connect Vault] 检测到跨库链接悬停事件`);
                console.log(`📋 Obsidian 传入参数: linkpath="${linkpath}", sourcePath="${sourcePath}", subpath="${subpath || ''}"`);
            }

            // 先检查是否是跨库链接格式
            if (!linkpath.includes(':')) {
                // 对于普通链接，静默处理，不输出日志
                return originalGetFileByPath.call(this.app.metadataCache, linkpath, sourcePath, subpath);
            }

            console.log(`✅ [Connect Vault] 确认是跨库链接格式`);
            console.log(`🔍 [Connect Vault] 开始查找完整链接...`);

            // 直接使用传入的参数，不进行复杂的文档搜索
            console.log(`🔧 [Connect Vault] 直接使用传入的链接: "${linkpath}"`);
            const linkTextToProcess = linkpath;

            // 检查链接路径是否符合我们的格式
            const match = linkTextToProcess.match(/^(.*?):(.*?)$/);
            if (match) {
                const [_, prefix, pathPart] = match;

                // 解析文件名和锚点
                let filename = pathPart;
                let anchor = subpath || '';

                // 如果没有子路径，尝试从路径部分解析
                if (!anchor) {
                    const headingMatch = pathPart.match(/^(.*?)#(.*)$/);
                    if (headingMatch) {
                        filename = headingMatch[1];
                        anchor = headingMatch[2];
                        console.log(`🔍 [Connect Vault] 从链接文本解析到锚点: "${anchor}"`);
                    }
                }

                // 如果还是没有锚点，但原始 linkpath 不包含锚点，说明可能是 parseLinktext 没有正确工作
                if (!anchor && linkpath !== linkTextToProcess) {
                    console.log(`⚠️ [Connect Vault] 从完整链接中重新解析锚点`);
                    const fullMatch = linkTextToProcess.match(/^(.*?):(.*?)#(.*)$/);
                    if (fullMatch) {
                        anchor = fullMatch[3];
                        console.log(`✅ [Connect Vault] 重新解析得到锚点: "${anchor}"`);
                    }
                }

                if (!anchor) {
                    console.log(`❌ [Connect Vault] 最终无法确定锚点，将创建无锚点的文件对象`);
                }

                console.log(`检测到特殊格式链接: 前缀=${prefix}, 文件名=${filename}, 锚点=${anchor}`);

                const connection = this.settings.connections.find(conn =>
                    conn.description === prefix
                );

                if (connection) {
                    // 构建完整路径（包含锚点）
                    const fullPath = anchor ? `${prefix}:${filename}#${anchor}` : `${prefix}:${filename}`;

                    // 创建一个虚拟文件对象
                    console.log(`为远程文件创建虚拟文件对象: ${fullPath}`);
                    // @ts-ignore - 创建一个虚拟文件对象，支持编辑
                    const virtualFile = {
                        path: fullPath,                    // 完整路径: "market:aa#^1"
                        name: filename + '.md',            // 文件名: "aa.md"
                        basename: filename,                // 基础名: "aa"
                        extension: 'md',
                        vault: this.app.vault,
                        // 添加一些必要的方法
                        toString: () => fullPath,
                        // 添加一个标记，表示这是一个远程文件
                        isRemoteFile: true,
                        // 保存锚点信息，供其他方法使用
                        anchor: anchor,
                        // 添加编辑支持的属性
                        stat: {
                            ctime: Date.now(),
                            mtime: Date.now(),
                            size: 0
                        },
                        // 让 Obsidian 认为这是一个可编辑的文件
                        parent: null,
                        deleted: false
                    };

                    console.log(`[Connect Vault] 创建虚拟文件对象: ${fullPath}, 锚点: ${anchor}, 支持编辑: true`);
                    return virtualFile;
                }
            }

            // 对于普通链接，使用原始方法
            return originalGetFileByPath.call(this.app.metadataCache, linkpath, sourcePath, subpath);
        };

        // 拦截 exists 方法，这样 Obsidian 就不会将远程文件标记为"未创建"
        this.register(() => {
            this.app.vault.adapter.exists = originalExists;
        });
        
        this.app.vault.adapter.exists = async (path: string) => {
            // 检查路径是否符合我们的格式 "前置内容:文件名"
            const match = path.match(/^(.*?):(.*?)$/);
            if (match) {
                const [_, prefix, filename] = match;
                console.log(`拦截到 exists 调用: ${path}, 前缀: ${prefix}, 文件名: ${filename}`);
                
                const connection = this.settings.connections.find(conn => 
                    conn.description === prefix
                );
                
                if (connection) {
                    // 对于远程文件，我们可以直接返回 true，表示文件存在
                    // 这样可以避免不必要的网络请求，并确保链接不会被标记为"未创建"
                    console.log(`远程文件 ${filename} 被标记为存在`);
                    return true;
                    
                    /* 原来的网络请求代码，如果需要实际检查文件是否存在，可以取消注释
                    try {
                        // 检查远程文件是否存在
                        console.log(`尝试检查远程文件是否存在: ${filename}, 端口: ${connection.port}`);
                        const response = await fetch(`http://127.0.0.1:${connection.port}/api/notes`);
                        if (!response.ok) {
                            console.log(`API 请求失败: ${response.status} ${response.statusText}`);
                            return false;
                        }
                        
                        const files = await response.json();
                        const exists = files.some((file: any) => file.name === filename);
                        console.log(`远程文件 ${filename} ${exists ? '存在' : '不存在'}`);
                        return exists;
                    } catch (error) {
                        console.error('检查远程文件存在性失败:', error);
                        return false;
                    }
                    */
                } else {
                    console.log(`未找到匹配的连接配置: ${prefix}`);
                }
            }
            
            // 如果不符合我们的格式或没有找到对应的连接，使用原始方法
            return originalExists.call(this.app.vault.adapter, path);
        };

        // 直接拦截 vault.cachedRead 方法
        this.register(() => {
            this.app.vault.cachedRead = originalVaultCachedRead;
            console.log('cachedRead 方法已恢复');
        });
        
        // 用新方法替换原始方法
        this.app.vault.cachedRead = async (file: any): Promise<string> => {
            // 获取文件路径
            const path = typeof file === 'string' ? file : file.path;
            console.log(`cachedRead 被调用: ${path}`);
            
            // 使用新的解析函数来处理跨库链接
            const parsed = this.parseCrossVaultLink(path);
            if (parsed) {
                let headParam = '';
                let idParam = '';

                // 根据锚点类型设置参数
                if (parsed.anchorType === 'heading') {
                    headParam = parsed.anchor;
                } else if (parsed.anchorType === 'block') {
                    // 对于块引用，移除 ^ 符号传递给 API
                    idParam = parsed.anchor.startsWith('^') ? parsed.anchor.substring(1) : parsed.anchor;
                }

                console.log(`拦截到 cachedRead 调用: ${path}`);
                console.log(`  前缀: ${parsed.prefix}, 文件名: ${parsed.filename}, 锚点: ${parsed.anchor}, 类型: ${parsed.anchorType}`);
                console.log(`  API 参数 - 章节: ${headParam}, 块: ${idParam}`);

                const connection = this.settings.connections.find(conn =>
                    conn.description === parsed.prefix
                );

                if (connection) {
                    try {
                        // 构建 API URL，支持章节和块参数
                        let apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(parsed.filename)}`;
                        if (headParam) {
                            apiUrl += `&head=${encodeURIComponent(headParam)}`;
                        } else if (idParam) {
                            apiUrl += `&id=${encodeURIComponent(idParam)}`;
                        }

                        console.log(`请求远程内容: ${apiUrl}`);
                        const response = await fetch(apiUrl);
                        if (!response.ok) {
                            console.log(`API 请求失败: ${response.status} ${response.statusText}`);
                            throw new Error('获取文件内容失败');
                        }

                        const data = await response.json();
                        if (data.error) {
                            throw new Error(data.error);
                        }

                        console.log(`成功获取远程内容: ${parsed.filename}, 类型: ${data.contentType}, 长度: ${data.content.length}`);

                        // 转换内容中的内部链接为跨库链接
                        const transformedContent = this.transformInternalLinks(data.content, parsed.prefix);
                        console.log(`内容转换完成，原始长度: ${data.content.length}, 转换后长度: ${transformedContent.length}`);

                        return transformedContent;
                    } catch (error) {
                        console.error('获取远程文件内容失败:', error);
                        throw new Error(`无法从远程库 ${parsed.prefix} 获取文件 ${parsed.filename}: ${error.message}`);
                    }
                } else {
                    console.log(`未找到匹配的连接配置: ${parsed.prefix}`);
                }
            }
            
            // 对于普通文件，调用原始方法
            return originalVaultCachedRead.call(this.app.vault, file);
        };

        // 拦截 getResourcePath 方法
        this.register(() => {
            this.app.vault.getResourcePath = originalGetResourcePath;
            console.log('getResourcePath 方法已恢复');
        });
        
        this.app.vault.getResourcePath = (file: any) => {
            // 检查文件路径是否符合我们的格式
            const path = typeof file === 'string' ? file : file.path;
            const match = path.match(/^(.*?):(.*?)$/);
            if (match) {
                const [_, prefix, filename] = match;
                console.log(`拦截到 getResourcePath 调用: ${path}, 前缀: ${prefix}, 文件名: ${filename}`);
                
                const connection = this.settings.connections.find(conn => 
                    conn.description === prefix
                );
                
                if (connection) {
                    // 构建远程资源路径
                    const resourcePath = `http://127.0.0.1:${connection.port}/api/resource?name=${encodeURIComponent(filename)}`;
                    console.log(`生成远程资源路径: ${resourcePath}`);
                    return resourcePath;
                } else {
                    console.log(`未找到匹配的连接配置: ${prefix}`);
                }
            }
            
            // 如果不符合我们的格式或没有找到对应的连接，使用原始方法
            return originalGetResourcePath.call(this.app.vault, file);
        };

        // 拦截 vault.modify 方法来处理跨库文件编辑
        const originalVaultModify = this.app.vault.modify.bind(this.app.vault);
        this.app.vault.modify = async (file: any, data: string) => {
            console.log(`[Connect Vault] 拦截到 vault.modify 调用: ${file.path}`);

            // 检查是否是远程文件
            if (file.isRemoteFile || (file.path && file.path.match(/^(.*?):(.*?)$/))) {
                console.log(`[Connect Vault] 检测到远程文件修改: ${file.path}`);
                return this.handleRemoteFileModify(file, data);
            }

            // 对于普通文件，使用原始方法
            return originalVaultModify(file, data);
        };

        // 拦截 openLinkText 方法来处理跨库链接跳转
        const originalOpenLinkText = this.app.workspace.openLinkText.bind(this.app.workspace);
        this.app.workspace.openLinkText = async (linktext: string, sourcePath: string, newLeaf?: any, openViewState?: any) => {
            console.log(`[Connect Vault] 拦截到 openLinkText 调用: "${linktext}"`);

            // 检查是否是跨库链接
            const match = linktext.match(/^(.*?):(.*?)$/);
            if (match) {
                const [_, prefix, pathWithAnchor] = match;

                // 解析文件路径和锚点
                let filename = pathWithAnchor;
                let anchor = '';

                // 检查是否有章节锚点 #
                const headingMatch = pathWithAnchor.match(/^(.*?)#(.*)$/);
                if (headingMatch) {
                    filename = headingMatch[1];
                    anchor = headingMatch[2];
                }

                console.log(`[Connect Vault] 跨库链接跳转: 前缀="${prefix}", 文件名="${filename}", 锚点="${anchor}"`);

                const connection = this.settings.connections.find(conn =>
                    conn.description === prefix
                );

                if (connection) {
                    // 构建 Obsidian URI - 尝试不同的格式
                    const encodedVaultName = encodeURIComponent(connection.vaultName);
                    let encodedFilename = encodeURIComponent(filename);

                    // 方法1: 将锚点直接包含在文件名中
                    if (anchor) {
                        if (anchor.startsWith('^')) {
                            // 块引用: filename#^blockid
                            encodedFilename = encodeURIComponent(`${filename}#^${anchor.substring(1)}`);
                        } else {
                            // 章节引用: filename#heading
                            encodedFilename = encodeURIComponent(`${filename}#${anchor}`);
                        }
                    }

                    let url = `obsidian://open?vault=${encodedVaultName}&file=${encodedFilename}`;

                    console.log(`[Connect Vault] 打开跨库链接 (方法1): ${url}`);
                    window.open(url);

                    // 如果方法1不工作，尝试方法2: 使用单独的参数
                    if (anchor) {
                        setTimeout(() => {
                            console.log(`[Connect Vault] 尝试方法2: 使用单独的锚点参数`);
                            const baseUrl = `obsidian://open?vault=${encodedVaultName}&file=${encodeURIComponent(filename)}`;
                            let alternativeUrl = baseUrl;

                            if (anchor.startsWith('^')) {
                                // 块引用
                                alternativeUrl += `&block=${encodeURIComponent(anchor)}`;
                            } else {
                                // 章节引用
                                alternativeUrl += `&heading=${encodeURIComponent(anchor)}`;
                            }

                            console.log(`[Connect Vault] 备用 URL: ${alternativeUrl}`);
                            // 注释掉自动打开，避免重复跳转
                            // window.open(alternativeUrl);
                        }, 1000);
                    }

                    return;
                } else {
                    console.log(`[Connect Vault] 未找到前缀 "${prefix}" 对应的连接配置`);
                }
            }

            // 如果不是跨库链接，使用原始方法
            return originalOpenLinkText(linktext, sourcePath, newLeaf, openViewState);
        };

        console.log('猴子补丁注册完成');
    }

    // 异步创建远程文件缓存
    async createRemoteFileCacheAsync(file: any, path: string): Promise<void> {
        try {
            const cache = await this.createRemoteFileCache(file, path);
            const cacheKey = typeof path === 'string' ? path : file.path;
            this.remoteCacheMap.set(cacheKey, cache);

            console.log(`远程文件缓存已创建并存储: ${cacheKey}`);

            // 触发重新渲染预览
            this.app.workspace.trigger('hover-link', {
                event: null,
                source: 'preview',
                hoverParent: null,
                targetEl: null,
                linktext: cacheKey
            });

        } catch (error) {
            console.error('异步创建远程文件缓存失败:', error);
        }
    }

    // 为远程文件创建缓存对象
    async createRemoteFileCache(_file: any, path: string): Promise<any> {
        try {
            console.log(`创建远程文件缓存: ${path}`);

            // 解析路径
            const match = path.match(/^(.*?):(.*?)$/);
            if (!match) {
                return this.createEmptyCache();
            }

            const [_, prefix, pathWithAnchor] = match;
            let filename = pathWithAnchor;
            let anchor = '';
            let anchorType = '';

            // 解析章节或块
            const headingMatch = pathWithAnchor.match(/^(.*?)#(.*)$/);
            if (headingMatch) {
                filename = headingMatch[1];
                anchor = headingMatch[2];
                anchorType = anchor.startsWith('^') ? 'block' : 'heading';
                if (anchorType === 'block') {
                    anchor = anchor.substring(1); // 移除 ^
                }
            }

            const connection = this.settings.connections.find(conn =>
                conn.description === prefix
            );

            if (!connection) {
                return this.createEmptyCache();
            }

            // 获取远程文件内容
            const apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}`;
            console.log(`获取远程文件内容用于缓存: ${apiUrl}`);

            const response = await fetch(apiUrl);
            if (!response.ok) {
                console.log(`API 请求失败: ${response.status}`);
                return this.createEmptyCache();
            }

            const data = await response.json();
            if (data.error) {
                console.log(`API 返回错误: ${data.error}`);
                return this.createEmptyCache();
            }

            // 解析文件内容，创建真实的缓存
            return this.parseRemoteFileContent(data.content, anchor, anchorType);

        } catch (error) {
            console.error('创建远程文件缓存失败:', error);
            return this.createEmptyCache();
        }
    }

    // 解析远程文件内容，创建缓存对象
    parseRemoteFileContent(content: string, targetAnchor: string, anchorType: string): any {
        console.log(`解析远程文件内容，目标锚点: "${targetAnchor}", 类型: ${anchorType}`);

        const lines = content.split('\n');
        const headings: any[] = [];
        const blocks: Record<string, any> = {};
        const sections: any[] = [];

        let currentOffset = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineStart = currentOffset;
            const lineEnd = currentOffset + line.length;

            // 解析标题
            const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
            if (headingMatch) {
                const level = headingMatch[1].length;
                const heading = headingMatch[2].trim();

                headings.push({
                    heading: heading,
                    level: level,
                    position: {
                        start: { line: i, col: 0, offset: lineStart },
                        end: { line: i, col: line.length, offset: lineEnd }
                    }
                });

                console.log(`找到标题: ${heading} (级别 ${level})`);
            }

            // 解析块ID
            const blockMatch = line.match(/\^([a-zA-Z0-9-_]+)\s*$/);
            if (blockMatch) {
                const blockId = blockMatch[1];
                blocks[blockId] = {
                    id: blockId,
                    position: {
                        start: { line: i, col: 0, offset: lineStart },
                        end: { line: i, col: line.length, offset: lineEnd }
                    }
                };

                console.log(`找到块ID: ${blockId}`);
            }

            currentOffset = lineEnd + 1; // +1 for newline
        }

        console.log(`解析完成: ${headings.length} 个标题, ${Object.keys(blocks).length} 个块`);

        return {
            links: [],
            embeds: [],
            tags: [],
            headings: headings,
            blocks: blocks,
            frontmatter: {},
            sections: sections,
            listItems: [],
            hash: "remote-file-" + Date.now(),
            stat: {
                ctime: Date.now(),
                mtime: Date.now(),
                size: content.length
            }
        };
    }

    // 创建空缓存对象
    createEmptyCache(): any {
        return {
            links: [],
            embeds: [],
            tags: [],
            headings: [],
            blocks: {},
            frontmatter: {},
            sections: [],
            listItems: [],
            hash: "empty-cache-" + Date.now(),
            stat: {
                ctime: Date.now(),
                mtime: Date.now(),
                size: 0
            }
        };
    }









    // 简化的链接查找方法 - 不进行复杂搜索
    findFullLinkInDocument(partialLink: string): string {
        console.log(`[Connect Vault] 简化模式：直接返回传入的链接 "${partialLink}"`);
        return partialLink;
    }

    // 通过光标位置确定用户当前悬停的具体链接（仅检查当前行）
    findCurrentHoveredLink(): string {
        try {
            const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView || !(activeView as any).editor) {
                return '';
            }

            const editor = (activeView as any).editor;
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);

            console.log(`📍 [Connect Vault] 光标位置: 第${cursor.line + 1}行, 第${cursor.ch + 1}列`);
            console.log(`📝 [Connect Vault] 当前行内容: "${line}"`);

            // 只在当前行查找跨库链接
            const linkRegex = /\[\[([^\]]+)\]\]/g;
            let match;
            const foundLinks = [];

            while ((match = linkRegex.exec(line)) !== null) {
                const linkText = match[1];
                if (linkText.includes(':')) {
                    const linkStart = match.index;
                    const linkEnd = match.index + match[0].length;

                    foundLinks.push({
                        text: linkText,
                        start: linkStart,
                        end: linkEnd,
                        fullLink: match[0]
                    });

                    console.log(`🔗 [Connect Vault] 发现跨库链接: ${match[0]} (位置: ${linkStart}-${linkEnd})`);
                }
            }

            if (foundLinks.length === 0) {
                console.log(`❌ [Connect Vault] 当前行没有跨库链接`);
                return '';
            }

            // 检查光标是否在某个链接范围内
            for (const link of foundLinks) {
                if (cursor.ch >= link.start && cursor.ch <= link.end) {
                    console.log(`🎯 [Connect Vault] ✅ 光标在链接 ${link.fullLink} 范围内`);
                    console.log(`🎉 [Connect Vault] 确定用户悬停的双链: [[${link.text}]]`);
                    return link.text;
                }
            }

            console.log(`❌ [Connect Vault] 光标不在任何跨库链接范围内`);
            console.log(`💡 [Connect Vault] 找到的链接: ${foundLinks.map(l => l.fullLink).join(', ')}`);
            console.log(`💡 [Connect Vault] 光标位置: ${cursor.ch}, 需要在链接范围内`);

            console.log(`[Connect Vault] 光标不在任何跨库链接范围内`);
            return '';

        } catch (e) {
            console.log('[Connect Vault] 查找当前悬停链接失败:', e.message);
            return '';
        }
    }

    // 仅用于调试：显示当前文档中的跨库链接（不解析，不缓存）
    scanCurrentDocumentForDebug() {
        try {
            const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView || !(activeView as any).editor) {
                console.log('[Connect Vault] 没有活动的编辑器');
                return;
            }

            const editor = (activeView as any).editor;
            const content = editor.getValue();

            // 只查找跨库链接，不解析
            const linkRegex = /\[\[([^\]]+)\]\]/g;
            let match;
            const crossVaultLinks = [];

            while ((match = linkRegex.exec(content)) !== null) {
                const linkText = match[1];
                if (linkText.includes(':')) {
                    crossVaultLinks.push(linkText);
                }
            }

            console.log(`[Connect Vault] 文档中的跨库链接 (${crossVaultLinks.length} 个):`);
            crossVaultLinks.forEach((link, index) => {
                console.log(`  ${index + 1}. [[${link}]]`);
            });

        } catch (e) {
            console.log('[Connect Vault] 扫描失败:', e.message);
        }
    }

    // 处理远程文件修改
    async handleRemoteFileModify(file: any, data: string): Promise<void> {
        console.log(`[Connect Vault] 处理远程文件修改: ${file.path}`);

        try {
            // 解析文件路径
            const match = file.path.match(/^(.*?):(.*?)$/);
            if (!match) {
                throw new Error('无效的远程文件路径格式');
            }

            const [_, prefix, pathWithAnchor] = match;
            let filename = pathWithAnchor;
            let anchor = '';
            let anchorType = '';

            // 解析章节或块
            const headingMatch = pathWithAnchor.match(/^(.*?)#(.*)$/);
            if (headingMatch) {
                filename = headingMatch[1];
                anchor = headingMatch[2];
                anchorType = anchor.startsWith('^') ? 'block' : 'heading';
                if (anchorType === 'block') {
                    anchor = anchor.substring(1); // 移除 ^
                }
            }

            console.log(`[Connect Vault] 远程文件修改详情: 前缀="${prefix}", 文件名="${filename}", 锚点="${anchor}", 类型="${anchorType}"`);

            // 查找连接配置
            const connection = this.settings.connections.find(conn =>
                conn.description === prefix
            );

            if (!connection) {
                throw new Error(`未找到前缀 "${prefix}" 对应的连接配置`);
            }

            // 构建 API URL
            let apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}&edit=true`;
            if (anchor) {
                if (anchorType === 'heading') {
                    apiUrl += `&head=${encodeURIComponent(anchor)}`;
                } else if (anchorType === 'block') {
                    apiUrl += `&id=${encodeURIComponent(anchor)}`;
                }
            }

            console.log(`[Connect Vault] 发送编辑请求: ${apiUrl}`);
            console.log(`[Connect Vault] 新内容长度: ${data.length}`);

            // 发送 POST 请求
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content: data })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API 请求失败: ${response.status} ${response.statusText} - ${errorData.error || '未知错误'}`);
            }

            const result = await response.json();
            console.log(`[Connect Vault] 远程文件修改成功:`, result);

            // 显示成功通知
            new Notice(`远程文件 "${prefix}:${filename}" 修改成功`);

            // 更新本地缓存
            this.remoteCacheMap.delete(file.path);

        } catch (error) {
            console.error('[Connect Vault] 远程文件修改失败:', error);
            new Notice(`远程文件修改失败: ${error.message}`, 5000);
            throw error; // 重新抛出错误，让 Obsidian 知道修改失败
        }
    }

}