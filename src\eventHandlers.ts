import { Plugin } from 'obsidian';
import { MyPluginSettings } from './settings';
import { parseCrossVaultLink } from './linkParser';
import { createHoverPopover, handleLinkClick, removeUnresolved } from './handlers';

export class EventHandlerManager {
    private plugin: Plugin;
    private settings: MyPluginSettings;
    private currentHoveredLinkText: string = '';
    private linkCache: Map<string, string> = new Map(); // 缓存链接信息
    private lastProcessedContent: string = ''; // 缓存上次处理的内容
    private debounceTimer: NodeJS.Timeout | null = null; // 防抖定时器

    constructor(plugin: Plugin, settings: MyPluginSettings) {
        this.plugin = plugin;
        this.settings = settings;
    }

    /**
     * 获取当前鼠标悬停的链接文本
     */
    getCurrentHoveredLinkText(): string {
        return this.currentHoveredLinkText;
    }

    /**
     * 从DOM元素重构完整的跨库链接文本
     */
    private reconstructFullLink(target: HTMLElement): string {
        const linkText = target.textContent?.replace(/^!/, '');

        // 1. 如果文本本身包含冒号，直接返回
        if (linkText && linkText.includes(':')) {
            console.log(`[Connect Vault] 🔍 文本本身包含完整链接: "${linkText}"`);
            return linkText;
        }

        // 2. 尝试从全局前缀映射获取
        if (linkText && window.crossVaultPrefixMap) {
            console.log(`[Connect Vault] 🔍 从前缀映射查找: "${linkText}"`);

            // 直接查找匹配
            const fullLink = window.crossVaultPrefixMap.get(linkText);
            if (fullLink) {
                console.log(`[Connect Vault] 🎯 从前缀映射找到: "${fullLink}"`);
                return fullLink;
            }

            // 查找包含该文件名的链接
            for (const [filename, fullLinkText] of window.crossVaultPrefixMap.entries()) {
                if (filename.includes(linkText) || fullLinkText.includes(linkText)) {
                    console.log(`[Connect Vault] 🎯 从前缀映射找到包含匹配: "${fullLinkText}"`);
                    return fullLinkText;
                }
            }
        }

        console.log(`[Connect Vault] 🔍 无法重构完整链接，返回空字符串`);
        return '';
    }

    /**
     * 注册所有事件处理器
     */
    registerAll(): void {
        this.registerGlobalMouseOverHandler();
        this.registerGlobalClickHandler();
        this.registerWorkspaceEvents();
        this.registerDOMObserver();
    }

    /**
     * 注册全局鼠标悬停事件处理器
     */
    private registerGlobalMouseOverHandler(): void {
        // 注册鼠标移动事件来跟踪当前悬停的链接
        this.plugin.registerDomEvent(document, 'mousemove', (evt: MouseEvent) => {
            const target = evt.target as HTMLElement;

            // 检查是否是链接元素
            const isLinkElement = target.classList.contains('cm-hmd-internal-link') ||
                (target.classList.contains('cm-underline') && target.closest('.cm-line')) ||
                target.classList.contains('internal-link') ||
                target.classList.contains('internal-embed');

            if (isLinkElement) {
                const linkText = target.textContent?.replace(/^!/, '');  // 移除嵌入标记

                // 尝试从周围的元素重构完整链接
                const fullLinkText = this.reconstructFullLink(target);

                if (fullLinkText && fullLinkText.includes(':')) {
                    // 只有当链接文本发生变化时才输出日志
                    if (this.currentHoveredLinkText !== fullLinkText) {
                        this.currentHoveredLinkText = fullLinkText;
                        console.log(`[Connect Vault] 🎯 鼠标悬停在跨库链接上: "${fullLinkText}"`);
                    }
                } else if (linkText && linkText.includes(':')) {
                    // 回退到原始逻辑
                    if (this.currentHoveredLinkText !== linkText) {
                        this.currentHoveredLinkText = linkText;
                        console.log(`[Connect Vault] 🎯 鼠标悬停在跨库链接上: "${linkText}"`);
                    }
                } else {
                    if (this.currentHoveredLinkText !== '') {
                        this.currentHoveredLinkText = '';
                    }
                }
            } else {
                if (this.currentHoveredLinkText !== '') {
                    this.currentHoveredLinkText = '';
                }
            }
        });

        // 原有的 Ctrl+悬停处理器
        this.plugin.registerDomEvent(document, 'mouseover', (evt: MouseEvent) => {
            if (!evt.ctrlKey) return;

            const target = evt.target as HTMLElement;

            console.log(`[Connect Vault] 🔍 Ctrl+悬停目标: "${target.textContent}" (类名: ${target.className})`);

            // 如果元素已经被处理过，则跳过
            if (target.hasAttribute('data-handled')) return;

            // 检查是否是链接元素
            const isLinkElement = target.classList.contains('cm-hmd-internal-link') ||
                (target.classList.contains('cm-underline') && target.closest('.cm-line')) ||
                target.classList.contains('internal-link') ||
                target.classList.contains('internal-embed');

            if (!isLinkElement) return;

            const linkText = target.textContent?.replace(/^!/, '');  // 移除嵌入标记

            // 只处理跨库链接
            if (!linkText || !linkText.includes(':')) return;

            console.log(`[Connect Vault] 🎯 检测到跨库链接悬停: "${linkText}"`);

            // 更新当前悬停的链接文本
            this.currentHoveredLinkText = linkText;

            const parsed = parseCrossVaultLink(linkText);
            if (!parsed) return;

            const connection = this.settings.connections.find(conn =>
                conn.description === parsed.prefix
            );

            if (!connection) {
                console.log(`[Connect Vault] ❌ 未找到前缀 "${parsed.prefix}" 的连接配置`);
                return;
            }

            console.log(`[Connect Vault] ✅ 找到连接配置，创建悬停预览`);

            // 标记元素已被处理
            target.setAttribute('data-handled', 'true');

            // 添加点击事件
            target.addEventListener('click', (clickEvt: MouseEvent) => {
                clickEvt.preventDefault();
                clickEvt.stopPropagation();
                handleLinkClick(this.plugin as any, clickEvt, target.textContent);
            });

            console.log(`[Connect Vault] 📝 解析结果: 文件="${parsed.filename}", 锚点="${parsed.anchor}"`);

            createHoverPopover(this.plugin as any, target, null, parsed.prefix, parsed.filename, connection, parsed.anchor);
        }, { capture: true });
    }

    /**
     * 注册全局点击事件处理器
     */
    private registerGlobalClickHandler(): void {
        this.plugin.registerDomEvent(document, 'click', (evt: MouseEvent) => {
            const target = evt.target as HTMLElement;
            if (target.classList.contains('internal-link') && !target.hasAttribute('data-handled')) {
                handleLinkClick(this.plugin as any, evt, target.textContent);
            }
        });
    }

    /**
     * 注册工作区事件
     */
    private registerWorkspaceEvents(): void {
        // 注册更多事件以确保在不同视图模式下都能捕获链接
        this.plugin.registerEvent(this.plugin.app.workspace.on('layout-change', () => {
            console.log('[Connect Vault] layout-change 事件触发');
            removeUnresolved(this.plugin as any);
        }));
        
        this.plugin.registerEvent(this.plugin.app.workspace.on('file-open', () => {
            // console.log('[Connect Vault] file-open 事件触发');
            removeUnresolved(this.plugin as any);
        }));

        this.plugin.registerEvent(this.plugin.app.workspace.on('active-leaf-change', () => {
            // console.log('[Connect Vault] active-leaf-change 事件触发');
            removeUnresolved(this.plugin as any);
        }));
    }

    /**
     * 注册简化的 DOM 观察器
     */
    private registerDOMObserver(): void {
        // 简化的观察器，主要用于触发 removeUnresolved
        const observer = new MutationObserver((mutations) => {
            const hasRelevantChanges = mutations.some(mutation =>
                Array.from(mutation.addedNodes).some(node =>
                    node instanceof Element && (
                        node.querySelector('.internal-link, .cm-hmd-internal-link, .markdown-embed') ||
                        node.classList.contains('internal-link') ||
                        node.classList.contains('cm-hmd-internal-link') ||
                        node.classList.contains('markdown-embed')
                    )
                )
            );
            if (hasRelevantChanges) {
                console.log('[Connect Vault] MutationObserver 检测到相关变化，触发 removeUnresolved');
                removeUnresolved(this.plugin as any);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class']
        });
    }
}
