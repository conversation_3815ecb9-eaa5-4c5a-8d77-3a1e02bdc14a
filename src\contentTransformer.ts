import { isCrossVaultLink } from './linkParser';

/**
 * 转换内容中的内部链接为跨库链接
 */
export function transformInternalLinks(content: string, prefix: string): string {
    console.log(`[Connect Vault] 开始转换内容中的内部链接，前缀: ${prefix}`);
    
    // 匹配所有的内部链接格式
    const linkRegex = /\[\[([^\]]+)\]\]/g;
    let matchCount = 0;
    
    const transformedContent = content.replace(linkRegex, (match, linkContent) => {
        matchCount++;
        console.log(`  处理链接 ${matchCount}: "${match}"`);
        
        let [linkPath, alias] = linkContent.split('|');
        
        // 检查是否已经是跨库链接
        if (isCrossVaultLink(linkPath)) {
            console.log(`  链接 ${matchCount}: "${linkPath}" 已经是跨库链接，跳过`);
            return match; // 保持原样
        }
        
        // 检查是否是特殊链接（如以 # 开头的当前文件内链接）
        // 但是 #^blockId 这种块引用应该被转换
        if (linkPath.startsWith('#') && !linkPath.startsWith('#^')) {
            console.log(`  链接 ${matchCount}: "${linkPath}" 是当前文件内章节链接，跳过`);
            return match; // 保持原样
        }
        
        // 转换为跨库链接
        const newLinkPath = `${prefix}:${linkPath}`;
        const result = alias ? `[[${newLinkPath}|${alias}]]` : `[[${newLinkPath}]]`;
        
        console.log(`  链接 ${matchCount}: "${match}" -> "${result}"`);
        return result;
    });
    
    console.log(`[Connect Vault] 内容转换完成，共处理 ${matchCount} 个链接`);
    return transformedContent;
}

/**
 * 处理嵌入内容的转换
 */
export function transformEmbedContent(content: string, prefix: string): string {
    // 匹配嵌入格式 ![[filename]]
    const embedRegex = /!\[\[([^\]]+)\]\]/g;
    
    return content.replace(embedRegex, (match, linkContent) => {
        let [linkPath, alias] = linkContent.split('|');
        
        // 检查是否已经是跨库链接
        if (isCrossVaultLink(linkPath)) {
            return match; // 保持原样
        }
        
        // 转换为跨库嵌入
        const newLinkPath = `${prefix}:${linkPath}`;
        return alias ? `![[${newLinkPath}|${alias}]]` : `![[${newLinkPath}]]`;
    });
}

/**
 * 清理内容中的特殊标记
 */
export function cleanContent(content: string): string {
    // 移除可能的元数据标记
    return content
        .replace(/^---\n[\s\S]*?\n---\n/, '') // 移除 YAML front matter
        .trim();
}

/**
 * 为内容添加来源信息
 */
export function addSourceInfo(content: string, sourcePath: string, prefix: string): string {
    const sourceInfo = `\n\n---\n*来源: ${prefix}:${sourcePath}*`;
    return content + sourceInfo;
}
