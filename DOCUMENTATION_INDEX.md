# Connect Vault Plugin - 文档索引

## 📚 文档列表

### 🚀 快速开始
- **[QUICK_START.md](QUICK_START.md)** - 5分钟快速上手指南
  - 基本安装和配置
  - 创建第一个跨库链接
  - 体验悬停预览功能

### 📖 详细指南
- **[USER_GUIDE.md](USER_GUIDE.md)** - 完整用户指南
  - 功能特性详解
  - 安装方法
  - 配置设置
  - 使用方法
  - 工作原理
  - 故障排除

### ⚙️ 配置示例
- **[CONFIGURATION_EXAMPLES.md](CONFIGURATION_EXAMPLES.md)** - 实际配置案例
  - 个人知识管理系统
  - 团队协作场景
  - 学术研究配置
  - 多语言内容管理
  - 高级配置技巧

### ✨ 新功能介绍
- **[SMART_PREFIX_HIDING.md](SMART_PREFIX_HIDING.md)** - 智能前缀隐藏功能
  - 功能概述和显示效果
  - 不同模式下的工作方式
  - 技术实现和使用示例
  - 配置选项和故障排除

## 🎯 根据需求选择文档

### 我是新用户
👉 从 [QUICK_START.md](QUICK_START.md) 开始

### 我需要详细了解功能
👉 阅读 [USER_GUIDE.md](USER_GUIDE.md)

### 我需要配置复杂场景
👉 参考 [CONFIGURATION_EXAMPLES.md](CONFIGURATION_EXAMPLES.md)

### 我想了解新功能
👉 查看 [SMART_PREFIX_HIDING.md](SMART_PREFIX_HIDING.md)

### 我遇到了问题
👉 查看 [USER_GUIDE.md](USER_GUIDE.md) 的故障排除部分

## 🔧 技术文档

### 开发相关
- **README.md** - 原始开发文档
- **package.json** - 项目依赖配置
- **tsconfig.json** - TypeScript 配置
- **manifest.json** - 插件清单文件

### 源代码结构
```
src/
├── handlers.ts      # 事件处理和悬停预览
├── server.ts        # API 服务器实现
├── settings.ts      # 设置界面
├── ui.ts           # UI 组件和样式
├── utils.ts        # 工具函数
└── monkey-patches.ts # Obsidian API 拦截
```

## 📋 功能清单

### ✅ 已实现功能
- [x] 跨库链接语法 `[[前缀:文件名]]`
- [x] Ctrl+悬停预览远程笔记
- [x] 智能文件名建议
- [x] 多库连接管理
- [x] API 服务器自动启动
- [x] 嵌套预览支持
- [x] 资源文件访问
- [x] **智能前缀隐藏**（新功能）

### 🔄 工作原理
1. **API 服务器**：每个库启动本地 HTTP 服务器
2. **链接拦截**：拦截 Obsidian 内部链接处理
3. **远程获取**：通过 API 获取远程库内容
4. **本地渲染**：在当前库中渲染远程内容

### 🎨 用户界面
- 设置面板：配置端口和连接
- 悬停预览：显示远程笔记内容
- 智能建议：输入时自动补全文件名

## 🆘 获取帮助

### 常见问题
1. **安装问题** → 查看 [QUICK_START.md](QUICK_START.md)
2. **配置问题** → 参考 [CONFIGURATION_EXAMPLES.md](CONFIGURATION_EXAMPLES.md)
3. **功能问题** → 阅读 [USER_GUIDE.md](USER_GUIDE.md)

### 调试信息
- 打开浏览器开发者工具（F12）
- 查看 Console 标签页的日志信息
- 搜索 "Connect Vault" 相关消息

### 技术支持
- 检查插件是否正确安装
- 验证端口配置是否正确
- 确认目标库已启用插件

## 🔄 版本信息

### 当前版本：v1.0.0
- 初始版本发布
- 支持基本的跨库链接和预览功能
- 提供完整的配置界面

### 系统要求
- Obsidian 0.15.0 或更高版本
- Node.js（开发环境）
- 支持的操作系统：Windows、macOS、Linux

## 📝 更新日志

### v1.0.0 (2024-01-15)
- ✨ 新功能：跨库链接支持
- ✨ 新功能：悬停预览
- ✨ 新功能：智能文件建议
- ✨ 新功能：多库连接管理
- 🐛 修复：TypeScript 编译错误
- 🐛 修复：HoverPopover API 使用问题
- 📚 文档：完整的用户指南和配置示例

## 🤝 贡献指南

欢迎贡献代码和文档：

1. **报告问题**：在 Issues 中描述遇到的问题
2. **建议功能**：提出新功能的想法和需求
3. **提交代码**：Fork 项目并提交 Pull Request
4. **改进文档**：帮助完善和翻译文档

## 📄 许可证

MIT License - 详见 LICENSE 文件
