import MyPlugin from '../main';

/**
 * 重构编辑模式下的完整链接
 */
function reconstructEditorModeLink(element: HTMLElement): string {
    const linkText = element.textContent || '';

    // 如果文本本身包含完整的跨库链接格式，直接返回
    if (linkText.includes(':')) {
        return linkText;
    }

    // 查找前面的兄弟元素或父元素中的前缀
    let currentElement: Node | null = element.previousSibling;
    while (currentElement) {
        const text = currentElement.textContent || '';
        // 查找类似 "test:" 的前缀
        const prefixMatch = text.match(/([^:\s\[\]]+):$/);
        if (prefixMatch) {
            return `${prefixMatch[1]}:${linkText}`;
        }
        currentElement = currentElement.previousSibling;
    }

    // 如果在兄弟元素中没找到，尝试在父元素的文本中查找
    const parentElement = element.parentElement;
    if (parentElement) {
        const parentText = parentElement.textContent || '';
        // 查找包含当前链接文本的完整跨库链接
        const fullLinkMatch = parentText.match(new RegExp(`([^:\\s\\[\\]]+):([^\\]]*${linkText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^\\]]*)`));
        if (fullLinkMatch) {
            return `${fullLinkMatch[1]}:${fullLinkMatch[2]}`;
        }
    }

    // 尝试从整个行的内容中提取
    const lineElement = element.closest('.cm-line');
    if (lineElement) {
        const lineText = lineElement.textContent || '';
        // 查找包含当前链接的完整跨库链接格式
        const linkRegex = /\[\[([^:\]]+:[^\]]*)\]\]/g;
        let match;
        while ((match = linkRegex.exec(lineText)) !== null) {
            const fullLink = match[1];
            if (fullLink.includes(linkText)) {
                return fullLink;
            }
        }
    }

    return '';
}

// 创建 Markdown 后处理器 - 简化版本，主要处理阅读模式
export function createMarkdownPostProcessor(plugin: MyPlugin) {
    return (element: HTMLElement, context: any) => {
        // console.log('[Connect Vault] === Markdown 后处理器被调用 ===');
        // console.log('[Connect Vault] 后处理器：元素类型:', element.tagName);
        // console.log('[Connect Vault] 后处理器：元素类名:', element.className);

        // 主要处理阅读模式的内部链接
        const links = element.querySelectorAll('a.internal-link');
        // console.log(`[Connect Vault] 后处理器：找到 ${links.length} 个内部链接`);

        // 如果没有找到链接，检查元素本身
        if (links.length === 0) {
            if (element.tagName === 'A' && element.classList.contains('internal-link')) {
                // console.log('[Connect Vault] 后处理器：元素本身就是内部链接');
                processLink(element, 0, plugin);
                return;
            }
        }

        links.forEach((link, index) => {
            processLink(link as HTMLElement, index, plugin);
        });

        // console.log('[Connect Vault] === Markdown 后处理器处理完成 ===');
    };
}

function processLink(link: HTMLElement, index: number, plugin: MyPlugin) {
    const text = link.textContent;
    // console.log(`[Connect Vault] 后处理器：处理链接 ${index + 1}: "${text}"`);

    // 检查是否是跨库链接（仅处理阅读模式）
    if (text && text.includes(':')) {
        const match = text.match(/^(.*?):(.*?)$/);
        if (match) {
            const [_, prefix, pathPart] = match;

            // 解析文件名和锚点
            let filename = pathPart;
            let anchor = '';

            const anchorMatch = pathPart.match(/^(.*?)#(.*)$/);
            if (anchorMatch) {
                filename = anchorMatch[1];
                anchor = anchorMatch[2];
            }

            // console.log(`[Connect Vault] 后处理器：跨库链接 - 前缀="${prefix}", 文件名="${filename}", 锚点="${anchor}"`);

            // 检查连接配置
            const connection = plugin.settings.connections.find(conn =>
                conn.description === prefix
            );

            if (!connection) {
                // console.log(`[Connect Vault] 后处理器：未找到前缀 "${prefix}" 的连接配置`);
                // console.log(`[Connect Vault] 后处理器：可用连接:`, plugin.settings.connections.map(c => c.description));
                return;
            }

            // console.log(`[Connect Vault] 后处理器：找到匹配的连接:`, connection);

            // 最简单的处理：直接修改文本内容
            if (!link.hasAttribute('data-processed')) {
                // console.log(`[Connect Vault] 后处理器：开始处理链接`);

                link.setAttribute('data-processed', 'true');
                link.setAttribute('data-original', text);
                link.setAttribute('data-filename', filename);

                // 默认只显示文件名
                link.textContent = filename;
                // console.log(`[Connect Vault] 后处理器：文本已修改为: "${filename}"`);

                // 添加悬停事件 - 只显示完整文本，让 Obsidian 内置预览接管
                const mouseEnterHandler = () => {
                    // console.log(`[Connect Vault] 后处理器：鼠标进入，显示完整文本`);
                    link.textContent = text;
                };

                const mouseLeaveHandler = () => {
                    // console.log(`[Connect Vault] 后处理器：鼠标离开，显示文件名`);
                    link.textContent = filename;
                };

                link.addEventListener('mouseenter', mouseEnterHandler);
                link.addEventListener('mouseleave', mouseLeaveHandler);

                // console.log(`[Connect Vault] 后处理器：事件监听器已添加`);
                // console.log(`[Connect Vault] 后处理器：处理完成: "${text}" -> "${filename}"`);
            } else {
                // console.log(`[Connect Vault] 后处理器：链接已处理过，跳过`);
            }
        } else {
            // console.log(`[Connect Vault] 后处理器：不是有效的跨库链接格式`);
        }
    } else {
        // console.log(`[Connect Vault] 后处理器：不是跨库链接，跳过`);
    }
}

// 处理单个内部链接
function processInternalLink(linkElement: HTMLElement, plugin: MyPlugin, index: number = 0) {
    const linkText = linkElement.textContent;
    console.log(`[Connect Vault] 后处理器：处理链接 ${index}: "${linkText}"`);

    if (!linkText) {
        console.log('[Connect Vault] 后处理器：链接文本为空');
        return;
    }

    // 解析跨库链接
    const match = linkText.match(/^(.*?):(.*?)$/);
    if (!match) {
        console.log(`[Connect Vault] 后处理器：链接 "${linkText}" 不是跨库格式`);
        return;
    }

    const [_, prefix, filename] = match;
    console.log(`[Connect Vault] 后处理器：解析到前缀 "${prefix}"，文件名 "${filename}"`);

    const connection = plugin.settings.connections.find(conn =>
        conn.description === prefix
    );

    if (!connection) {
        console.log(`[Connect Vault] 后处理器：未找到前缀 "${prefix}" 对应的连接配置`);
        return;
    }

    // 检查是否已经处理过，如果是则重置
    if (linkElement.hasAttribute('data-cross-vault-processed')) {
        console.log(`[Connect Vault] 后处理器：链接已处理过，重置处理`);
        resetReadingModeLink(linkElement);
    }

    console.log(`[Connect Vault] 后处理器：开始处理链接 "${linkText}"`);
    linkElement.setAttribute('data-cross-vault-processed', 'true');

    // 保存原始信息
    linkElement.setAttribute('data-original-text', linkText);
    linkElement.setAttribute('data-prefix', prefix);
    linkElement.setAttribute('data-filename', filename);

    // 创建智能显示结构
    createSmartLinkStructure(linkElement, prefix, filename);
}

// 重置阅读模式链接
function resetReadingModeLink(linkElement: HTMLElement) {
    // 移除所有相关的类和属性
    linkElement.classList.remove('cross-vault-smart-link', 'show-full-text');
    linkElement.removeAttribute('data-display-text');

    // 克隆节点以移除所有事件监听器
    const newElement = linkElement.cloneNode(true) as HTMLElement;
    linkElement.parentNode?.replaceChild(newElement, linkElement);

    console.log('[Connect Vault] 后处理器：链接已重置');
}

// 创建智能链接结构 - 不修改原文，使用 CSS 隐藏
function createSmartLinkStructure(linkElement: HTMLElement, prefix: string, filename: string) {
    console.log(`[Connect Vault] 后处理器：创建智能结构 - 前缀: "${prefix}", 文件名: "${filename}"`);

    // 保存原始文本，但不修改
    const originalText = linkElement.textContent;
    linkElement.setAttribute('data-original-text', originalText || '');
    linkElement.setAttribute('data-prefix', prefix);
    linkElement.setAttribute('data-filename', filename);

    // 使用 CSS 类来控制显示，而不是修改内容
    linkElement.classList.add('cross-vault-smart-link');

    // 添加伪元素样式来显示只有文件名的版本
    linkElement.setAttribute('data-display-text', filename);

    // 设置交互行为
    setupReadingModeInteraction(linkElement, prefix, filename);

    console.log(`[Connect Vault] 后处理器：智能结构创建完成，原文保持不变`);
}

// 设置阅读模式交互 - 使用 CSS 类切换
function setupReadingModeInteraction(linkElement: HTMLElement, prefix: string, filename: string) {
    let isHovered = false;
    let isFocused = false;

    const showFullText = () => {
        linkElement.classList.add('show-full-text');
        console.log(`[Connect Vault] 后处理器：显示完整文本 "${prefix}:${filename}"`);
    };

    const showFilenameOnly = () => {
        linkElement.classList.remove('show-full-text');
        console.log(`[Connect Vault] 后处理器：只显示文件名 "${filename}"`);
    };

    // 鼠标事件
    linkElement.addEventListener('mouseenter', () => {
        isHovered = true;
        showFullText();
    });

    linkElement.addEventListener('mouseleave', () => {
        isHovered = false;
        if (!isFocused) {
            showFilenameOnly();
        }
    });

    // 焦点事件
    linkElement.addEventListener('focus', () => {
        isFocused = true;
        showFullText();
    });

    linkElement.addEventListener('blur', () => {
        isFocused = false;
        if (!isHovered) {
            showFilenameOnly();
        }
    });

    // 键盘事件（用于检测 Tab 导航）
    linkElement.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            showFullText();
        }
    });
}
