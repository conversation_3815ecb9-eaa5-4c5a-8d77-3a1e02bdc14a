# Connect Vault Plugin - 快速开始

## 5分钟快速上手

### 第一步：安装插件

1. 将插件文件复制到 `.obsidian/plugins/connect-vault/` 目录
2. 在 Obsidian 设置中启用 "Connect Vault" 插件

### 第二步：基本配置

1. 打开 Obsidian 设置 → 社区插件 → Connect Vault
2. 设置当前库的端口号（默认 3000）
3. 点击"测试连接"确认 API 服务器正常运行

### 第三步：添加远程库

假设您有两个库：主库（当前）和工作库

1. 在工作库中也安装此插件，设置端口为 3001
2. 回到主库，点击"添加连接"
3. 配置连接：
   - 端口号：3001
   - 前置内容：work
   - 库名：工作库

### 第四步：创建跨库链接

在主库中输入：
```markdown
查看 [[work:会议记录]]
```

### 第五步：体验智能前缀隐藏

1. 正常情况下，链接显示为：`会议记录`（前缀被隐藏）
2. 当光标在链接所在行时，显示为：`work:会议记录`
3. 鼠标悬停在链接上时，也会显示完整前缀

### 第六步：体验悬停预览

1. 按住 Ctrl 键
2. 鼠标悬停在 `[[work:会议记录]]` 链接上
3. 查看弹出的预览窗口

## 常用语法

```markdown
[[库前缀:文件名]]
```

示例：
- `[[work:项目计划]]` - 工作库中的项目计划
- `[[personal:日记]]` - 个人库中的日记
- `[[archive:旧文档]]` - 归档库中的旧文档

## 智能建议

输入 `[[work:` 时会自动显示工作库中的文件建议列表。

## 故障排除

**链接不工作？**
1. 检查前缀是否正确配置
2. 确认目标库已启用插件
3. 验证端口号设置

**预览不显示？**
1. 确认按住了 Ctrl 键
2. 检查控制台是否有错误信息

## 新功能：智能前缀隐藏

✨ **更清洁的阅读体验**：
- 正常情况下只显示文件名
- 光标在附近或悬停时显示完整前缀
- 详细说明请查看 [智能前缀隐藏功能](SMART_PREFIX_HIDING.md)

## 下一步

- 阅读完整的 [用户指南](USER_GUIDE.md)
- 了解 [智能前缀隐藏功能](SMART_PREFIX_HIDING.md)
- 配置更多库连接
- 探索嵌套预览功能
