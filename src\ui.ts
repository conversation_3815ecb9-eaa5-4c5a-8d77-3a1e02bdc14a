import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Editor, EditorPosition, EditorSuggest, EditorSuggestContext, EditorSuggestTriggerInfo } from 'obsidian';
import MyPlugin from '../main';

export class SampleModal extends Modal {
    constructor(app: App) {
        super(app);
    }

    onOpen() {
        const {contentEl} = this;
        contentEl.setText('Woah!');
    }

    onClose() {
        const {contentEl} = this;
        contentEl.empty();
    }
}

export class FilenameSuggest extends EditorSuggest<string> {
    plugin: MyPlugin;

    constructor(app: App, plugin: MyPlugin) {
        super(app);
        this.plugin = plugin;
    }

    onTrigger(cursor: EditorPosition, editor: Editor): EditorSuggestTriggerInfo | null {
        const line = editor.getLine(cursor.line);
        const subString = line.substring(0, cursor.ch);
        
        // 检查是否是自定义链接格式
        const customMatch = subString.match(/\[\[([^[\]]*?):(.*?)$/);
        if (customMatch) {
            const prefix = customMatch[1];
            const partial = customMatch[2] || '';
            const connection = this.plugin.settings.connections.find(conn => 
                conn.description === prefix
            );

            if (connection) {
                return {
                    start: {
                        line: cursor.line,
                        ch: cursor.ch - partial.length
                    },
                    end: cursor,
                    query: partial
                };
            }
        }

        // 检查是否是原生链接格式
        const nativeMatch = subString.match(/\[\[(.*?)$/);
        if (nativeMatch) {
            return {
                start: {
                    line: cursor.line,
                    ch: cursor.ch - nativeMatch[1].length
                },
                end: cursor,
                query: nativeMatch[1]
            };
        }

        return null;
    }

    async getSuggestions(context: EditorSuggestContext): Promise<string[]> {
        const line = context.editor.getLine(context.start.line);
        const prefix = line.match(/\[\[([^[\]]*?):/)?.[1];
        const connection = this.plugin.settings.connections.find(conn => 
            conn.description === prefix
        );

        if (!connection) return [];

        try {
            const response = await fetch(`http://127.0.0.1:${connection.port}/api/notes`);
            if (!response.ok) throw new Error('获取文件列表失败');
            
            const files = await response.json();
            const query = context.query.toLowerCase();
            
            return files
                .map((file: { name: string }) => file.name)
                .filter((name: string) => 
                    !query || name.toLowerCase().includes(query)
                );
        } catch (error) {
            console.error('获取文件列表失败:', error);
            return [];
        }
    }

    renderSuggestion(value: string, el: HTMLElement): void {
        el.createEl('div', { text: value });
    }

    selectSuggestion(value: string, evt: MouseEvent | KeyboardEvent): void {
        const { context } = this;
        if (!context) return;

        const { editor, start, end } = context;
        editor.replaceRange(value + ']]', start, end);
        editor.setCursor(start.line, start.ch + value.length + 2);
    }
}

export function loadStyles() {
    const styleEl = document.createElement('style');
    styleEl.id = 'connect-vault-styles';
    document.head.appendChild(styleEl);

    styleEl.textContent = `
        .popover-title {
            padding: 6px 10px;
            background-color: var(--background-secondary);
            border-bottom: 1px solid var(--background-modifier-border);
            font-weight: 600;
        }

        .popover-content {
            padding: 10px;
            max-height: 400px;
            overflow-y: auto;
        }

        .popover-error {
            color: var(--text-error);
        }

        /* 为不同库的链接添加样式支持 */
        .internal-link[class*=" "],
        .cm-hmd-internal-link[class*=" "],
        a.internal-link[class*=" "],
        .cm-underline[class*=" "] {
            text-decoration: underline;
        }

        /* 跨库链接智能前缀隐藏样式 - 真正不修改原文的方案 */

        /* 智能链接的基础样式 */
        .cross-vault-smart-link {
            position: relative;
            color: var(--text-accent);
        }

        /* 默认隐藏原文，显示文件名 */
        .cross-vault-smart-link:not(.show-full-text) {
            font-size: 0; /* 隐藏原文 */
        }

        .cross-vault-smart-link:not(.show-full-text)::after {
            content: attr(data-display-text);
            font-size: var(--font-text-size);
            color: var(--text-accent);
            font-weight: inherit;
        }

        /* 显示完整文本时恢复原文 */
        .cross-vault-smart-link.show-full-text {
            font-size: var(--font-text-size);
            color: var(--text-accent);
        }

        /* 确保链接整体样式一致 */
        .cm-hmd-internal-link,
        .internal-link {
            text-decoration: none;
            border-bottom: 1px solid var(--text-accent-hover);
            transition: all 0.2s ease-in-out;
        }

        .cm-hmd-internal-link:hover,
        .internal-link:hover {
            background-color: var(--background-modifier-hover);
        }

        /* 编辑模式的智能链接样式 */
        .cm-hmd-internal-link.cross-vault-smart-link:not(.show-full-text) {
            font-size: 0;
        }

        .cm-hmd-internal-link.cross-vault-smart-link:not(.show-full-text)::after {
            content: attr(data-display-text);
            font-size: var(--font-text-size);
            color: var(--text-accent);
        }

        .cm-hmd-internal-link.cross-vault-smart-link.show-full-text {
            font-size: var(--font-text-size);
        }
    `;
}