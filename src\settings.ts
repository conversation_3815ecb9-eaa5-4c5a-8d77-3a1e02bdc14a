import { App, PluginSettingTab, Setting, Notice } from 'obsidian';
import MyPlugin from '../main';

export interface MyPluginSettings {
    mySetting: string;
    port: number;
    connections: Array<{
        port: number;
        description: string;
        vaultName: string;
    }>;
}

export const DEFAULT_SETTINGS: MyPluginSettings = {
    mySetting: 'default',
    port: 3000,
    connections: []
}

export class SampleSettingTab extends PluginSettingTab {
    plugin: MyPlugin;

    constructor(app: App, plugin: MyPlugin) {
        super(app, plugin);
        this.plugin = plugin;
    }

    display(): void {
        const {containerEl} = this;
        containerEl.empty();

        new Setting(containerEl)
            .setName('Setting #1')
            .setDesc('It\'s a secret')
            .addText(text => text
                .setPlaceholder('Enter your secret')
                .setValue(this.plugin.settings.mySetting)
                .onChange(async (value) => {
                    this.plugin.settings.mySetting = value;
                    await this.plugin.saveSettings();
                }));

        new Setting(containerEl)
            .setName('API Server Port')
            .setDesc('设置 API 服务器端口号')
            .addText(text => text
                .setPlaceholder('3000')
                .setValue(String(this.plugin.settings.port))
                .onChange(async (value) => {
                    this.plugin.settings.port = Number(value);
                    await this.plugin.saveSettings();
                }));

        new Setting(containerEl)
            .setName('测试按钮')
            .setDesc('点击测试 API 连接')
            .addButton(button => button
                .setButtonText('测试连接')
                .onClick(async () => {
                    try {
                        const response = await fetch(`http://127.0.0.1:${this.plugin.settings.port}/api/notes`);
                        if (response.ok) {
                            const data = await response.json();
                            new Notice(`连接成功！共找到 ${data.length} 个笔记文件`);
                        } else {
                            new Notice('连接失败：' + response.statusText);
                        }
                    } catch (error) {
                        new Notice('连接失败：' + error.message);
                    }
                }));

        // 修改显示已保存连接的部分
        this.plugin.settings.connections.forEach(conn => {
            new Setting(containerEl)
                .addText(text => text
                    .setPlaceholder('端口号')
                    .setValue(String(conn.port))
                    .onChange(async (value) => {
                        conn.port = Number(value);
                        await this.plugin.saveSettings();
                    }))
                .addText(text => text
                    .setPlaceholder('前置内容')
                    .setValue(conn.description)
                    .onChange(async (value) => {
                        conn.description = value;
                        await this.plugin.saveSettings();
                    }))
                .addText(text => text
                    .setPlaceholder('库名')
                    .setValue(conn.vaultName || '')
                    .onChange(async (value) => {
                        conn.vaultName = value;
                        await this.plugin.saveSettings();
                    }))
                .addButton(btn => btn
                    .setButtonText('删除')
                    .onClick(async () => {
                        const index = this.plugin.settings.connections.indexOf(conn);
                        if (index > -1) {
                            this.plugin.settings.connections.splice(index, 1);
                            await this.plugin.saveSettings();
                            this.display();
                        }
                    }));
        });

        // 修改添加新连接的部分
        new Setting(containerEl)
            .setName('添加连接')
            .setDesc('添加其他端口的连接')
            .addButton(button => button
                .setButtonText('添加')
                .onClick(async () => {
                    const newConn = {
                        port: 3000,
                        description: '',
                        vaultName: ''
                    };
                    this.plugin.settings.connections.push(newConn);
                    await this.plugin.saveSettings();
                    this.display();
                }));
    }
}