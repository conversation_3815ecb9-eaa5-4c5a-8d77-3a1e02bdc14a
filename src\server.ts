import { Notice } from 'obsidian';
import * as http from 'http';
import MyPlugin from '../main';
import { getMimeType } from './utils';

export async function initializeServer(plugin: MyPlugin): Promise<http.Server> {
    try {
        const server = http.createServer(async (req, res) => {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            // 获取所有笔记列表
            if (req.url === '/api/notes' && req.method === 'GET') {
                const files = plugin.app.vault.getMarkdownFiles();
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(files.map(file => ({
                    path: file.path,
                    name: file.basename
                }))));
                return;
            }

            // 获取指定笔记的基本信息
            if (req.url?.startsWith('/api/note/info?') && req.method === 'GET') {
                try {
                    const urlParams = new URL(req.url, `http://localhost:${plugin.settings.port}`);
                    const fileName = urlParams.searchParams.get('name');

                    console.log(`[Connect Vault] Info API请求: name=${fileName}`);

                    if (!fileName) {
                        res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({ error: '缺少 name 参数' }));
                        return;
                    }

                    const file = plugin.app.vault.getMarkdownFiles().find(f => f.basename === fileName);

                    if (!file) {
                        res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
                        res.end(JSON.stringify({
                            error: `文件 "${fileName}" 不存在`,
                            exists: false
                        }));
                        return;
                    }

                    // 获取文件的基本信息
                    const fileInfo = await extractFileInfo(plugin, file);

                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        name: fileName,
                        path: file.path,
                        exists: true,
                        info: fileInfo
                    }));

                } catch (error) {
                    console.error('[Connect Vault] Info API错误:', error);
                    res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({
                        error: '服务器内部错误',
                        details: error.message
                    }));
                }
                return;
            }

            // 获取指定笔记的 frontmatter
            if (req.url?.startsWith('/api/note/frontmatter?') && req.method === 'GET') {
                try {
                    const urlParams = new URL(req.url, `http://localhost:${plugin.settings.port}`);
                    const fileName = urlParams.searchParams.get('name');

                    // console.log(`[Connect Vault] Frontmatter API请求: name=${fileName}`);

                    if (!fileName) {
                        res.writeHead(400);
                        res.end(JSON.stringify({ error: '缺少 name 参数' }));
                        return;
                    }

                    const file = plugin.app.vault.getMarkdownFiles().find(f => f.basename === fileName);

                    if (!file) {
                        res.writeHead(404);
                        res.end(JSON.stringify({ error: `文件 "${fileName}" 不存在` }));
                        return;
                    }

                    // 获取文件的 frontmatter
                    const frontmatter = await extractFrontmatter(plugin, file);

                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        name: fileName,
                        path: file.path,
                        frontmatter: frontmatter
                    }));

                } catch (error) {
                    console.error('[Connect Vault] Frontmatter API错误:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({ error: '服务器内部错误' }));
                }
                return;
            }

            // 获取指定笔记内容 - 支持章节和块参数
            if (req.url?.startsWith('/api/note?') && (req.method === 'GET' || req.method === 'POST')) {
                const urlParams = new URL(req.url, `http://localhost:${plugin.settings.port}`);
                const editMode = urlParams.searchParams.get('edit') === 'true';

                // 处理 POST 请求（编辑模式）
                if (req.method === 'POST' && editMode) {
                    handleNoteEdit(req, res, plugin);
                    return;
                }

                // 处理 GET 请求（读取模式）
                try {
                    const urlParams = new URL(req.url, `http://localhost:${plugin.settings.port}`);
                    const fileName = urlParams.searchParams.get('name');
                    const headParam = urlParams.searchParams.get('head');
                    const idParam = urlParams.searchParams.get('id');

                    // console.log(`[Connect Vault] API请求: name=${fileName}, head=${headParam}, id=${idParam}`);

                    if (!fileName) {
                        res.writeHead(400);
                        res.end(JSON.stringify({ error: '缺少 name 参数' }));
                        return;
                    }

                    const file = plugin.app.vault.getMarkdownFiles().find(f => f.basename === fileName);

                    if (!file) {
                        res.writeHead(404);
                        res.end(JSON.stringify({ error: `文件 "${fileName}" 不存在` }));
                        return;
                    }

                    const content = await plugin.app.vault.read(file);
                    let extractedContent = content;
                    let contentType = 'full';
                    let targetInfo = '';

                    // 处理章节参数
                    if (headParam) {
                        // console.log(`[Connect Vault] 提取章节: ${headParam}`);
                        const headingResult = extractHeadingContent(content, headParam);

                        if (!headingResult) {
                            res.writeHead(404);
                            res.end(JSON.stringify({ error: `章节 "${headParam}" 不存在` }));
                            return;
                        }

                        extractedContent = headingResult;
                        contentType = 'heading';
                        targetInfo = headParam;
                    }
                    // 处理块参数
                    else if (idParam) {
                        // console.log(`[Connect Vault] 提取块: ${idParam}`);
                        const blockResult = extractBlockContent(content, idParam);

                        if (!blockResult) {
                            res.writeHead(404);
                            res.end(JSON.stringify({ error: `块 "${idParam}" 不存在` }));
                            return;
                        }

                        extractedContent = blockResult;
                        contentType = 'block';
                        targetInfo = idParam;
                    }

                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({
                        name: file.basename,
                        path: file.path,
                        content: extractedContent,
                        contentType: contentType,
                        target: targetInfo,
                        fullLength: content.length,
                        extractedLength: extractedContent.length
                    }));

                } catch (error) {
                    console.error('[Connect Vault] API错误:', error);
                    res.writeHead(500);
                    res.end(JSON.stringify({ error: '服务器内部错误: ' + error.message }));
                }
                return;
            }

            // 获取资源文件
            if (req.url?.startsWith('/api/resource?name=') && req.method === 'GET') {
                const fileName = decodeURIComponent(req.url.slice('/api/resource?name='.length));
                const file = plugin.app.vault.getFiles().find(f => f.basename === fileName);
                
                if (!file) {
                    res.writeHead(404);
                    res.end(JSON.stringify({ error: '资源文件不存在' }));
                    return;
                }

                try {
                    const arrayBuffer = await plugin.app.vault.readBinary(file);
                    const buffer = Buffer.from(arrayBuffer);
                    
                    // 设置适当的内容类型
                    const mimeType = getMimeType(file.extension);
                    res.writeHead(200, { 'Content-Type': mimeType });
                    res.end(buffer);
                } catch (err) {
                    res.writeHead(500);
                    res.end(JSON.stringify({ error: '读取资源文件失败' }));
                }
                return;
            }

            res.writeHead(404);
            res.end('Not Found');
        });

        server.listen(plugin.settings.port, '127.0.0.1', () => {
            new Notice(`API Server running on port ${plugin.settings.port}`);
        });

        server.on('error', (err: Error) => {
            console.error('Server error:', err);
            new Notice('Failed to start API server: ' + err.message);
        });

        return server;
    } catch (error) {
        console.error('Server initialization error:', error);
        new Notice('Failed to initialize API server: ' + error.message);
        throw error;
    }
}

// 提取章节内容
function extractHeadingContent(content: string, headingName: string): string | null {
    // console.log(`[Connect Vault] 开始提取章节: "${headingName}"`);

    const lines = content.split('\n');
    let startIndex = -1;
    let endIndex = lines.length;
    let targetLevel = 0;

    // 查找目标章节
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 检查各级标题
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
        if (headingMatch) {
            const level = headingMatch[1].length;
            const title = headingMatch[2].trim();

            // console.log(`[Connect Vault] 找到标题: 级别${level}, 内容"${title}"`);

            if (title === headingName || title.includes(headingName)) {
                // console.log(`[Connect Vault] 匹配到目标章节: "${title}"`);
                startIndex = i;
                targetLevel = level;
                break;
            }
        }
    }

    if (startIndex === -1) {
        // console.log(`[Connect Vault] 未找到章节: "${headingName}"`);
        return null;
    }

    // 查找下一个同级或更高级标题作为结束位置
    for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i].trim();
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);

        if (headingMatch) {
            const level = headingMatch[1].length;
            if (level <= targetLevel) {
                endIndex = i;
                console.log(`[Connect Vault] 找到章节结束位置: 行${i}`);
                break;
            }
        }
    }

    const extractedLines = lines.slice(startIndex, endIndex);
    const result = extractedLines.join('\n').trim();

    console.log(`[Connect Vault] 章节提取完成: ${extractedLines.length}行, ${result.length}字符`);
    return result;
}

// 提取块内容
function extractBlockContent(content: string, blockId: string): string | null {
    console.log(`[Connect Vault] 开始提取块: "${blockId}"`);

    // 确保 blockId 不带 ^ 前缀（统一处理）
    const cleanBlockId = blockId.startsWith('^') ? blockId.substring(1) : blockId;
    console.log(`[Connect Vault] 清理后的块ID: "${cleanBlockId}"`);

    const lines = content.split('\n');
    let blockIdLineIndex = -1;

    // 1. 查找块ID标记所在的行
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // 检查行末是否有块ID标记 ^blockId
        if (line.includes(`^${cleanBlockId}`)) {
            console.log(`[Connect Vault] 找到块标记在第${i + 1}行: "${line}"`);
            blockIdLineIndex = i;
            break;
        }

        // 也检查独立行的块ID标记
        if (line.trim() === `^${cleanBlockId}`) {
            console.log(`[Connect Vault] 找到独立块标记在第${i + 1}行: "${line}"`);
            blockIdLineIndex = i;
            break;
        }
    }

    if (blockIdLineIndex === -1) {
        console.log(`[Connect Vault] 未找到块: "${cleanBlockId}"`);

        // 调试：显示文件中所有的块标记
        console.log(`[Connect Vault] 调试：文件中的所有块标记:`);
        lines.forEach((line, index) => {
            if (line.includes('^')) {
                console.log(`  第${index + 1}行: "${line}"`);
            }
        });

        return null;
    }

    // 2. 向上查找块的开始位置（直到遇到空行或文档开始）
    let blockStartIndex = 0;
    for (let i = blockIdLineIndex - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line === '') {
            // 遇到空行，块从下一行开始
            blockStartIndex = i + 1;
            console.log(`[Connect Vault] 找到块开始位置（空行后）: 第${blockStartIndex + 1}行`);
            break;
        }
    }

    // 如果没有遇到空行，块从文档开始
    if (blockStartIndex === 0) {
        console.log(`[Connect Vault] 块从文档开始`);
    }

    // 3. 提取块内容（从块开始到块ID行，不包括块ID行）
    let blockEndIndex = blockIdLineIndex;

    // 检查块ID是否在独立行上
    const blockIdLine = lines[blockIdLineIndex];
    if (blockIdLine.trim() === `^${cleanBlockId}`) {
        // 块ID在独立行上，块内容不包括这一行
        blockEndIndex = blockIdLineIndex;
    } else {
        // 块ID在内容行末尾，需要去掉块ID标记
        const contentWithoutBlockId = blockIdLine.replace(new RegExp(`\\s*\\^${cleanBlockId}\\s*$`), '');
        if (contentWithoutBlockId.trim()) {
            // 该行有内容，包括这一行但去掉块ID
            lines[blockIdLineIndex] = contentWithoutBlockId;
            blockEndIndex = blockIdLineIndex + 1;
        } else {
            // 该行只有块ID，不包括这一行
            blockEndIndex = blockIdLineIndex;
        }
    }

    // 4. 提取并返回块内容
    const blockLines = lines.slice(blockStartIndex, blockEndIndex);
    const blockContent = blockLines.join('\n').trim();

    console.log(`[Connect Vault] 块提取完成:`);
    console.log(`  - 块范围: 第${blockStartIndex + 1}行 到 第${blockEndIndex}行`);
    console.log(`  - 块行数: ${blockLines.length}`);
    console.log(`  - 块内容: "${blockContent}"`);

    return blockContent || null;
}

// 处理笔记编辑请求
async function handleNoteEdit(req: any, res: any, plugin: any) {
    console.log('[Connect Vault] 处理笔记编辑请求');

    try {
        // 解析 URL 参数
        const urlParams = new URL(req.url, `http://localhost:${plugin.settings.port}`);
        const fileName = urlParams.searchParams.get('name');
        const headParam = urlParams.searchParams.get('head');
        const idParam = urlParams.searchParams.get('id');

        if (!fileName) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: '缺少 name 参数' }));
            return;
        }

        // 读取请求体
        let body = '';
        req.on('data', (chunk: any) => {
            body += chunk.toString();
        });

        req.on('end', async () => {
            try {
                const requestData = JSON.parse(body);
                const newContent = requestData.content;

                if (typeof newContent !== 'string') {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'content 字段必须是字符串' }));
                    return;
                }

                console.log(`[Connect Vault] 编辑请求: name=${fileName}, head=${headParam}, id=${idParam}`);
                console.log(`[Connect Vault] 新内容长度: ${newContent.length}`);

                // 查找文件
                const file = plugin.app.vault.getMarkdownFiles().find((f: any) => f.basename === fileName);
                if (!file) {
                    res.writeHead(404, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: `文件 "${fileName}" 不存在` }));
                    return;
                }

                // 读取原始文件内容
                const originalContent = await plugin.app.vault.read(file);
                let updatedContent = originalContent;

                // 根据参数类型进行不同的替换
                if (headParam) {
                    // 替换章节内容
                    updatedContent = replaceHeadingContent(originalContent, headParam, newContent);
                    if (updatedContent === originalContent) {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: `章节 "${headParam}" 不存在` }));
                        return;
                    }
                } else if (idParam) {
                    // 替换块内容
                    updatedContent = replaceBlockContent(originalContent, idParam, newContent);
                    if (updatedContent === originalContent) {
                        res.writeHead(404, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: `块 "${idParam}" 不存在` }));
                        return;
                    }
                } else {
                    // 替换整个文件内容
                    updatedContent = newContent;
                }

                // 写入文件
                await plugin.app.vault.modify(file, updatedContent);

                console.log(`[Connect Vault] 文件 "${fileName}" 编辑成功`);

                // 返回成功响应
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    name: fileName,
                    path: file.path,
                    contentType: headParam ? 'heading' : (idParam ? 'block' : 'full'),
                    target: headParam || idParam || '',
                    originalLength: originalContent.length,
                    newLength: updatedContent.length
                }));

            } catch (parseError) {
                console.error('[Connect Vault] 解析请求体失败:', parseError);
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: '无效的 JSON 格式' }));
            }
        });

    } catch (error) {
        console.error('[Connect Vault] 编辑请求处理失败:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: '服务器内部错误: ' + error.message }));
    }
}

// 替换章节内容
function replaceHeadingContent(content: string, headingName: string, newContent: string): string {
    console.log(`[Connect Vault] 开始替换章节: "${headingName}"`);

    const lines = content.split('\n');
    let startIndex = -1;
    let endIndex = lines.length;
    let targetLevel = 0;

    // 查找目标章节
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);

        if (headingMatch) {
            const level = headingMatch[1].length;
            const title = headingMatch[2].trim();

            if (title === headingName || title.includes(headingName)) {
                console.log(`[Connect Vault] 找到目标章节: "${title}" (级别 ${level})`);
                startIndex = i;
                targetLevel = level;
                break;
            }
        }
    }

    if (startIndex === -1) {
        console.log(`[Connect Vault] 未找到章节: "${headingName}"`);
        return content; // 返回原内容表示未找到
    }

    // 查找章节结束位置
    for (let i = startIndex + 1; i < lines.length; i++) {
        const line = lines[i].trim();
        const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);

        if (headingMatch) {
            const level = headingMatch[1].length;
            if (level <= targetLevel) {
                endIndex = i;
                console.log(`[Connect Vault] 找到章节结束位置: 行${i}`);
                break;
            }
        }
    }

    // 替换章节内容
    const beforeSection = lines.slice(0, startIndex);
    const afterSection = lines.slice(endIndex);
    const newLines = newContent.split('\n');

    const result = [...beforeSection, ...newLines, ...afterSection].join('\n');

    console.log(`[Connect Vault] 章节替换完成`);
    return result;
}

// 替换块内容
function replaceBlockContent(content: string, blockId: string, newContent: string): string {
    console.log(`[Connect Vault] 开始替换块: "${blockId}"`);

    // 确保 blockId 不带 ^ 前缀（统一处理）
    const cleanBlockId = blockId.startsWith('^') ? blockId.substring(1) : blockId;
    console.log(`[Connect Vault] 清理后的块ID: "${cleanBlockId}"`);

    const lines = content.split('\n');
    let blockIdLineIndex = -1;

    // 1. 查找块ID标记所在的行
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // 检查行末是否有块ID标记 ^blockId
        if (line.includes(`^${cleanBlockId}`)) {
            console.log(`[Connect Vault] 找到块标记在第${i + 1}行: "${line}"`);
            blockIdLineIndex = i;
            break;
        }

        // 也检查独立行的块ID标记
        if (line.trim() === `^${cleanBlockId}`) {
            console.log(`[Connect Vault] 找到独立块标记在第${i + 1}行: "${line}"`);
            blockIdLineIndex = i;
            break;
        }
    }

    if (blockIdLineIndex === -1) {
        console.log(`[Connect Vault] 未找到块: "${cleanBlockId}"`);
        return content; // 返回原内容表示未找到
    }

    // 2. 向上查找块的开始位置（直到遇到空行或文档开始）
    let blockStartIndex = 0;
    for (let i = blockIdLineIndex - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line === '') {
            // 遇到空行，块从下一行开始
            blockStartIndex = i + 1;
            console.log(`[Connect Vault] 找到块开始位置（空行后）: 第${blockStartIndex + 1}行`);
            break;
        }
    }

    // 如果没有遇到空行，块从文档开始
    if (blockStartIndex === 0) {
        console.log(`[Connect Vault] 块从文档开始`);
    }

    // 3. 确定块的结束位置和块ID标记的处理方式
    let blockEndIndex;
    let blockMarker = '';
    let isIndependentBlockId = false;

    // 检查块ID是否在独立行上
    const blockIdLine = lines[blockIdLineIndex];
    if (blockIdLine.trim() === `^${cleanBlockId}`) {
        // 块ID在独立行上
        isIndependentBlockId = true;
        blockMarker = `^${cleanBlockId}`;
        blockEndIndex = blockIdLineIndex + 1; // 包括块ID行在内都要被替换
        console.log(`[Connect Vault] 块ID在独立行上，块范围包括块ID行`);
    } else {
        // 块ID在内容行末尾，提取块标记
        const blockMarkerMatch = blockIdLine.match(/\s*\^[a-zA-Z0-9-_]+\s*$/);
        blockMarker = blockMarkerMatch ? blockMarkerMatch[0] : ` ^${cleanBlockId}`;
        blockEndIndex = blockIdLineIndex + 1; // 包括带块ID的内容行
        console.log(`[Connect Vault] 块ID在内容行末尾，块标记: "${blockMarker}"`);
    }

    // 4. 构建新的文档内容
    const beforeBlock = lines.slice(0, blockStartIndex);
    const afterBlock = lines.slice(blockEndIndex);

    // 处理新内容
    const newContentLines = newContent.split('\n');

    // 根据块ID的位置决定如何添加块标记
    let replacementLines;
    if (isIndependentBlockId) {
        // 独立块ID行：新内容 + 独立的块ID行
        replacementLines = [...newContentLines, blockMarker];
    } else {
        // 内容行末尾的块ID：最后一行内容后面加块标记
        const lastLineIndex = newContentLines.length - 1;
        if (lastLineIndex >= 0) {
            newContentLines[lastLineIndex] += blockMarker;
        }
        replacementLines = newContentLines;
    }

    // 5. 组合最终结果
    const result = [...beforeBlock, ...replacementLines, ...afterBlock];

    console.log(`[Connect Vault] 块替换完成:`);
    console.log(`  - 原块范围: 第${blockStartIndex + 1}行 到 第${blockEndIndex}行`);
    console.log(`  - 新内容行数: ${newContentLines.length}`);
    console.log(`  - 块标记: "${blockMarker}"`);

    return result.join('\n');
}

// 提取文件的 frontmatter
async function extractFrontmatter(plugin: MyPlugin, file: any): Promise<any> {
    try {
        console.log(`[Connect Vault] 开始提取 frontmatter: ${file.basename}`);

        // 方法1: 使用 Obsidian 的 metadataCache 获取 frontmatter
        const fileCache = plugin.app.metadataCache.getFileCache(file);
        if (fileCache && fileCache.frontmatter) {
            console.log(`[Connect Vault] 从缓存获取 frontmatter:`, fileCache.frontmatter);

            // 移除 Obsidian 内部属性
            const cleanFrontmatter = { ...fileCache.frontmatter };
            delete cleanFrontmatter.position;

            return cleanFrontmatter;
        }

        // 方法2: 手动解析文件内容中的 frontmatter
        const content = await plugin.app.vault.read(file);
        const frontmatter = parseFrontmatterFromContent(content);

        if (frontmatter) {
            console.log(`[Connect Vault] 手动解析 frontmatter:`, frontmatter);
            return frontmatter;
        }

        console.log(`[Connect Vault] 文件没有 frontmatter`);
        return {};

    } catch (error) {
        console.error(`[Connect Vault] 提取 frontmatter 失败:`, error);
        return {};
    }
}

// 手动解析内容中的 frontmatter
function parseFrontmatterFromContent(content: string): any {
    try {
        // 检查是否以 --- 开头
        if (!content.startsWith('---\n')) {
            return null;
        }

        // 查找结束的 ---
        const endIndex = content.indexOf('\n---\n', 4);
        if (endIndex === -1) {
            return null;
        }

        // 提取 frontmatter 内容
        const frontmatterText = content.substring(4, endIndex);
        console.log(`[Connect Vault] 解析 frontmatter 文本:`, frontmatterText);

        // 简单的 YAML 解析（支持基本格式）
        const frontmatter: any = {};
        const lines = frontmatterText.split('\n');

        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine || trimmedLine.startsWith('#')) {
                continue; // 跳过空行和注释
            }

            // 解析 key: value 格式
            const colonIndex = trimmedLine.indexOf(':');
            if (colonIndex > 0) {
                const key = trimmedLine.substring(0, colonIndex).trim();
                let value: any = trimmedLine.substring(colonIndex + 1).trim();

                // 处理不同的值类型
                if (value.startsWith('"') && value.endsWith('"')) {
                    // 字符串值（带引号）
                    value = value.slice(1, -1);
                } else if (value.startsWith('[') && value.endsWith(']')) {
                    // 数组值
                    try {
                        value = JSON.parse(value);
                    } catch {
                        // 如果 JSON 解析失败，尝试简单的数组解析
                        value = value.slice(1, -1).split(',').map((item: string) => item.trim().replace(/['"]/g, ''));
                    }
                } else if (value === 'true' || value === 'false') {
                    // 布尔值
                    value = value === 'true';
                } else if (!isNaN(Number(value))) {
                    // 数字值
                    value = Number(value);
                }

                frontmatter[key] = value;
            }
        }

        return Object.keys(frontmatter).length > 0 ? frontmatter : null;

    } catch (error) {
        console.error(`[Connect Vault] 解析 frontmatter 失败:`, error);
        return null;
    }
}

// 提取文件的基本信息
async function extractFileInfo(plugin: MyPlugin, file: any): Promise<any> {
    try {
        // console.log(`[Connect Vault] 开始提取文件信息: ${file.basename}`);

        // 获取 vault 的绝对路径
        let vaultPath = '';
        let absolutePath = file.path;

        try {
            // 尝试获取 vault 的根目录路径
            const adapter = plugin.app.vault.adapter as any;
            if (adapter.basePath) {
                vaultPath = adapter.basePath;
            } else if (adapter.path) {
                vaultPath = adapter.path;
            } else if (adapter.fs && adapter.fs.basePath) {
                vaultPath = adapter.fs.basePath;
            } else {
                // 尝试从 vault 名称推断路径
                const vaultName = plugin.app.vault.getName();
                vaultPath = vaultName || 'Unknown Vault';
            }

            // 构建绝对路径
            if (vaultPath && vaultPath !== 'Unknown Vault') {
                absolutePath = `${vaultPath}/${file.path}`.replace(/\\/g, '/');
            }
        } catch (error) {
            // console.log(`[Connect Vault] 获取 vault 路径失败:`, error);
            vaultPath = plugin.app.vault.getName() || 'Unknown Vault';
        }

        // 解析路径信息
        const pathParts = file.path.split('/');
        const fileName = pathParts[pathParts.length - 1];
        const folderPath = pathParts.length > 1 ? pathParts.slice(0, -1).join('/') : '';

        const fileInfo: any = {
            // 基本信息
            basename: file.basename,
            name: fileName,
            extension: file.extension,

            // 路径信息
            path: file.path,                    // 库内相对路径
            relativePath: file.path,            // 库内相对路径 (别名)
            absolutePath: absolutePath,         // 系统绝对路径
            vaultPath: vaultPath,              // Vault 根目录绝对路径
            vaultName: plugin.app.vault.getName(), // Vault 名称

            // 目录信息
            folderPath: folderPath,             // 文件所在文件夹的相对路径
            folderName: folderPath ? pathParts[pathParts.length - 2] : '', // 直接父文件夹名
            depth: pathParts.length - 1,       // 文件夹深度
            pathSegments: pathParts,           // 路径分段

            // 文件统计
            size: file.stat?.size || 0,
            created: file.stat?.ctime || 0,
            modified: file.stat?.mtime || 0,
            exists: true
        };

        // 获取文件的元数据缓存
        const fileCache = plugin.app.metadataCache.getFileCache(file);
        if (fileCache) {
            fileInfo.metadata = {
                headings: fileCache.headings?.length || 0,
                blocks: Object.keys(fileCache.blocks || {}).length,
                links: fileCache.links?.length || 0,
                embeds: fileCache.embeds?.length || 0,
                tags: fileCache.tags?.length || 0,
                sections: fileCache.sections?.length || 0,
                listItems: fileCache.listItems?.length || 0
            };

            // 添加标题列表
            if (fileCache.headings && fileCache.headings.length > 0) {
                fileInfo.headings = fileCache.headings.map((heading: any) => ({
                    heading: heading.heading,
                    level: heading.level
                }));
            }

            // 添加块引用列表
            if (fileCache.blocks && Object.keys(fileCache.blocks).length > 0) {
                fileInfo.blocks = Object.keys(fileCache.blocks);
            }

            // 添加标签列表
            if (fileCache.tags && fileCache.tags.length > 0) {
                fileInfo.tags = fileCache.tags.map((tag: any) => tag.tag);
            }

            // 添加链接列表
            if (fileCache.links && fileCache.links.length > 0) {
                fileInfo.links = fileCache.links.map((link: any) => ({
                    link: link.link,
                    displayText: link.displayText
                }));
            }
        }

        // 获取 frontmatter
        const frontmatter = await extractFrontmatter(plugin, file);
        if (frontmatter && Object.keys(frontmatter).length > 0) {
            fileInfo.frontmatter = frontmatter;
        }

        // 获取文件内容的基本统计
        try {
            const content = await plugin.app.vault.read(file);
            const lines = content.split('\n');
            fileInfo.content = {
                length: content.length,
                lines: lines.length,
                words: content.split(/\s+/).filter(word => word.length > 0).length,
                characters: content.length,
                charactersNoSpaces: content.replace(/\s/g, '').length
            };
        } catch (error) {
            console.error(`[Connect Vault] 读取文件内容失败:`, error);
            fileInfo.content = {
                length: 0,
                lines: 0,
                words: 0,
                characters: 0,
                charactersNoSpaces: 0
            };
        }

        // console.log(`[Connect Vault] 文件信息提取完成:`, fileInfo);
        return fileInfo;

    } catch (error) {
        console.error(`[Connect Vault] 提取文件信息失败:`, error);
        return {
            basename: file.basename,
            path: file.path,
            exists: true,
            error: '提取信息失败'
        };
    }
}