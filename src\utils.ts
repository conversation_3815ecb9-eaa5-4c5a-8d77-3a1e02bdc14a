export function getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'svg': 'image/svg+xml',
        'pdf': 'application/pdf',
        'mp3': 'audio/mpeg',
        'mp4': 'video/mp4',
        'webm': 'video/webm',
        'ogg': 'audio/ogg',
        'wav': 'audio/wav',
        'md': 'text/markdown',
        'txt': 'text/plain',
        'json': 'application/json',
        'css': 'text/css',
        'js': 'application/javascript',
        'html': 'text/html',
        'xml': 'application/xml',
    };
    
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

export function parseLinkText(linkText: string | null, defaultPrefix: string = '') {
    if (!linkText) {
        return { matchPrefix: defaultPrefix, matchFilename: '' };
    }
    
    // 移除 Markdown 链接语法
    linkText = linkText.replace(/^\!?\[\[|\]\]$/g, '');
    
    let matchPrefix = defaultPrefix;
    let matchFilename = linkText;
    
    // 检查是否有自定义前缀
    const customMatch = linkText.match(/^(.*?):(.*?)$/);
    if (customMatch) {
        [, matchPrefix, matchFilename] = customMatch;
    }
    
    console.log(`解析链接文本: ${linkText}, 默认前缀: ${defaultPrefix}, 结果前缀: ${matchPrefix}, 文件名: ${matchFilename}`);
    
    return { matchPrefix, matchFilename };
}