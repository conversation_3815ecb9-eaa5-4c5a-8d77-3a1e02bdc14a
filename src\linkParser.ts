import { CrossVaultLinkInfo } from './types';

/**
 * 解析跨库链接文本，提取前缀、文件名和锚点信息
 */
export function parseCrossVaultLink(linkText: string): CrossVaultLinkInfo | null {
    // 匹配格式: prefix:filename 或 prefix:filename#anchor
    const match = linkText.match(/^(.*?):(.*?)(?:#(.*))?$/);
    if (!match) {
        return null;
    }

    const [_, prefix, filename, anchor = ''] = match;
    
    let anchorType: 'heading' | 'block' | null = null;
    if (anchor) {
        anchorType = anchor.startsWith('^') ? 'block' : 'heading';
    }

    return {
        prefix: prefix.trim(),
        filename: filename.trim(),
        anchor: anchor.trim(),
        anchorType
    };
}

/**
 * 检查链接文本是否为跨库链接格式
 */
export function isCrossVaultLink(linkText: string): boolean {
    return linkText.includes(':') && parseCrossVaultLink(linkText) !== null;
}

/**
 * 构建API URL用于获取远程内容
 */
export function buildApiUrl(baseUrl: string, filename: string, anchor?: string): string {
    let apiUrl = `${baseUrl}/api/note?name=${encodeURIComponent(filename)}`;
    
    if (anchor) {
        if (anchor.startsWith('^')) {
            // 块引用，移除 ^ 符号传递给 API
            apiUrl += `&id=${encodeURIComponent(anchor.substring(1))}`;
        } else {
            // 章节引用
            apiUrl += `&head=${encodeURIComponent(anchor)}`;
        }
    }
    
    return apiUrl;
}

/**
 * 从链接文本中提取显示文本（用于前缀隐藏）
 */
export function extractDisplayText(linkText: string): string {
    const parsed = parseCrossVaultLink(linkText);
    if (!parsed) {
        return linkText;
    }
    
    // 返回文件名和锚点部分，不包括前缀
    return parsed.anchor ? `${parsed.filename}#${parsed.anchor}` : parsed.filename;
}

/**
 * 生成 Obsidian URI 用于跨库跳转
 */
export function generateObsidianUri(vaultName: string, filename: string, anchor?: string): string {
    let uri = `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename)}`;
    
    if (anchor) {
        if (anchor.startsWith('^')) {
            // 块引用
            uri += `&block=${encodeURIComponent(anchor)}`;
        } else {
            // 章节引用
            uri += `&heading=${encodeURIComponent(anchor)}`;
        }
    }
    
    return uri;
}
