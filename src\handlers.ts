import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HoverPopover, Notice } from 'obsidian';
import MyPlugin from '../main';
import { parseLinkText } from './utils';

// 添加全局变量声明
declare global {
    interface Window {
        activeHoverPopover: HoverPopover | null;
        activeHoverPopoverTarget: HTMLElement | null;
    }
}

// 初始化全局变量
window.activeHoverPopover = null;
window.activeHoverPopoverTarget = null;

// 处理嵌入内容的函数
export async function handleEmbed(plugin: MyPlugin, embedParent: Element) {
    const embedLink = embedParent.querySelector('.internal-embed');
    if (!embedLink) return;
    
    const linkText = embedLink.textContent?.replace(/^\!?\[\[|\]\]$/g, '');
    const match = linkText?.match(/^(.*?):(.*?)$/);
    if (!match) return;

    const [_, prefix, filename] = match;
    const connection = plugin.settings.connections.find(conn => 
        conn.description === prefix
    );
    
    if (!connection) return;

    // 阻止默认的嵌入渲染
    embedParent.empty();
    embedParent.classList.remove('is-unresolved');
    embedParent.classList.add('is-loaded');

    // 创建自定义嵌入容器
    const contentEl = embedParent.createDiv({
        cls: 'markdown-embed-content'
    });
    
    try {
        const response = await fetch(`http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}`);
        const data = await response.json();
        
        // 添加标题栏
        const titleEl = embedParent.createDiv({
            cls: 'markdown-embed-title'
        });
        titleEl.setText(`${prefix}:${filename}`);
        
        // 渲染内容
        await MarkdownRenderer.renderMarkdown(
            data.content,
            contentEl,
            '',
            plugin
        );

        // 处理嵌入内容中的链接
        contentEl.querySelectorAll('.internal-link').forEach(link => {
            link.classList.remove('is-unresolved');
            handleInternalLink(plugin, link, prefix);
        });
    } catch (error) {
        contentEl.setText('获取嵌入内容失败: ' + error.message);
    }
}

// 处理普通链接的函数
export function handleNormalLink(plugin: MyPlugin, link: Element) {
    const linkText = link.textContent;
    const match = linkText?.match(/^(.*?):(.*?)$/);
    if (match) {
        const [_, prefix] = match;
        const connection = plugin.settings.connections.find(conn => 
            conn.description === prefix
        );
        if (connection) {
            link.classList.remove('is-unresolved');
            // 添加对应库名的class
            if (connection.vaultName) {
                link.classList.add(connection.vaultName);
            }
        }
    }
}

// 移除带前置内容双链的未解析状态
export function removeUnresolved(plugin: MyPlugin) {
    // console.log('[Connect Vault] removeUnresolved 函数被调用');

    // 处理普通链接 - 包括编辑模式、实时预览和阅读模式下的链接
    const allLinks = document.querySelectorAll('.internal-link, .cm-hmd-internal-link, a.internal-link, .cm-underline');
    // console.log(`[Connect Vault] 找到 ${allLinks.length} 个链接元素`);

    allLinks.forEach(link => {
        handleNormalLink(plugin, link);
    });

    // 处理嵌入内容
    const embedElements = document.querySelectorAll('.markdown-embed');
    // console.log(`[Connect Vault] 找到 ${embedElements.length} 个嵌入元素`);

    embedElements.forEach(embedParent => {
        handleEmbed(plugin, embedParent);
    });

    // 应用智能前缀隐藏功能
    // console.log('[Connect Vault] 开始应用智能前缀隐藏功能');
    applyCrossVaultLinkHiding(plugin);
}

// 辅助函数：处理链接点击
export function handleLinkClick(plugin: MyPlugin, evt: MouseEvent, linkText: string | null, defaultPrefix: string = '') {
    const { matchPrefix, matchFilename } = parseLinkText(linkText, defaultPrefix);
    const connection = plugin.settings.connections.find(conn => 
        conn.description === matchPrefix
    );
    
    if (connection?.vaultName) {
        evt.preventDefault();
        evt.stopPropagation();
        const encodedVaultName = encodeURIComponent(connection.vaultName);
        const encodedFilename = encodeURIComponent(matchFilename);
        const url = `obsidian://open?vault=${encodedVaultName}&file=${encodedFilename}`;
        window.open(url);
    }
}

// 处理内部链接的函数
export function handleInternalLink(plugin: MyPlugin, link: Element, defaultPrefix: string = '') {
    // 移除未解析标记
    link.classList.remove('is-unresolved');
    
    // 检查是否已经处理过这个链接
    if (link.hasAttribute('data-handled')) return;
    link.setAttribute('data-handled', 'true');
    
    // 解析链接文本
    const { matchPrefix, matchFilename } = parseLinkText(link.textContent, defaultPrefix);
    
    // 查找对应的连接配置
    const connection = plugin.settings.connections.find(conn => 
        conn.description === matchPrefix
    );
    
    if (!connection) return;
    
    // 添加点击事件
    link.addEventListener('click', (evt: MouseEvent) => {
        evt.preventDefault();
        evt.stopPropagation();
        handleLinkClick(plugin, evt, link.textContent, defaultPrefix);
    });

    // 悬停预览 - 只在按住 Ctrl 键时触发
    link.addEventListener('mouseover', (evt: MouseEvent) => {
        // 确保只有在按住 Ctrl 键时才触发预览
        if (!evt.ctrlKey && !evt.metaKey) return; // 添加对 Mac 的支持 (metaKey)
        
        // 防止重复创建悬停窗口
        if (link.querySelector('.hover-popover')) return;
        
        // 解析完整的链接文本以获取正确的文件名和锚点
        const fullLinkText = link.textContent || '';
        const parsed = plugin.parseCrossVaultLink(fullLinkText);

        createHoverPopover(
            plugin,
            link as HTMLElement,
            evt,
            matchPrefix,
            parsed ? parsed.filename : matchFilename,
            connection,
            parsed ? parsed.anchor : ''
        );
    });
    
    // 不要在这里添加全局键盘事件监听器，而是在插件初始化时添加一次
}

// 全局键盘事件处理函数 - 在插件初始化时添加一次
export function setupGlobalKeyHandler() {
    // 移除可能存在的旧事件监听器
    document.removeEventListener('keyup', handleGlobalKeyUp);
    document.addEventListener('keyup', handleGlobalKeyUp);
}

// 全局键盘事件处理函数
function handleGlobalKeyUp(evt: KeyboardEvent) {
    if (evt.key === 'Control' || evt.key === 'Meta') {
        // 如果有活动的悬停窗口，则关闭它
        if (window.activeHoverPopover) {
            window.activeHoverPopover.unload();
            if (window.activeHoverPopoverTarget) {
                window.activeHoverPopoverTarget.removeAttribute('data-has-popover');
            }
            window.activeHoverPopover = null;
            window.activeHoverPopoverTarget = null;
        }
    }
}

// 创建悬浮窗的通用函数
export function createHoverPopover(
    plugin: MyPlugin,
    targetEl: HTMLElement,
    event: MouseEvent | null,
    prefix: string,
    filename: string,
    connection: any,
    anchor: string = ''
) {
    // 检查是否已经有悬停窗口
    if (targetEl.querySelector('.hover-popover')) return;
    
    // 使用全局变量跟踪当前活动的悬停窗口
    if (window.activeHoverPopover) {
        // 如果当前目标已经是活动目标，则不要再创建新的悬停窗口
        if (window.activeHoverPopoverTarget === targetEl) {
            return;
        }
        
        // 关闭现有的悬停窗口
        window.activeHoverPopover.unload();
        if (window.activeHoverPopoverTarget) {
            window.activeHoverPopoverTarget.removeAttribute('data-has-popover');
        }
        window.activeHoverPopover = null;
        window.activeHoverPopoverTarget = null;
    }
    
    // 检查是否已经有全局悬停窗口标记
    const existingPopover = targetEl.getAttribute('data-has-popover');
    if (existingPopover === 'true') return;
    
    // 标记元素已有悬停窗口
    targetEl.setAttribute('data-has-popover', 'true');
    
    // 创建悬停窗口
    const hoverParent: any = { hoverPopover: null };
    const popover = new HoverPopover(hoverParent, targetEl);
    hoverParent.hoverPopover = popover;
    const popoverEl = popover.hoverEl;
    
    // 保存到全局变量
    window.activeHoverPopover = popover;
    window.activeHoverPopoverTarget = targetEl;
    
    // 设置样式
    popoverEl.style.width = '500px';
    popoverEl.style.maxHeight = '400px';
    popoverEl.style.overflow = 'auto';
    
    // 显示加载中
    const loadingEl = popoverEl.createDiv('popover-loading');
    loadingEl.setText('加载中...');
    
    // 添加鼠标移出事件，确保悬停窗口关闭
    const handleMouseLeave = (e: MouseEvent) => {
        // 检查鼠标是否真的离开了元素和悬停窗口
        const toElement = e.relatedTarget as HTMLElement;
        if (targetEl.contains(toElement) || (popoverEl && popoverEl.contains(toElement))) {
            return;
        }
        
        // 检查是否仍然按住 Ctrl 键
        if (e.ctrlKey || e.metaKey) return;
        
        popover.unload();
        targetEl.removeAttribute('data-has-popover');
        window.activeHoverPopover = null;
        window.activeHoverPopoverTarget = null;
        
        // 移除事件监听器
        targetEl.removeEventListener('mouseleave', handleMouseLeave);
        popoverEl.removeEventListener('mouseleave', handleMouseLeave);
    };
    
    targetEl.addEventListener('mouseleave', handleMouseLeave);
    popoverEl.addEventListener('mouseleave', handleMouseLeave);
    
    // 获取内容并渲染
    const loadContent = async () => {
        try {
            console.log(`尝试获取悬停预览内容: ${filename}, 前缀: ${prefix}, 端口: ${connection.port}, 锚点: ${anchor}`);

            // 构建API URL，支持锚点参数
            let apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}`;
            if (anchor) {
                if (anchor.startsWith('^')) {
                    // 块引用，移除 ^ 符号传递给 API
                    apiUrl += `&id=${encodeURIComponent(anchor.substring(1))}`;
                } else {
                    // 章节引用
                    apiUrl += `&head=${encodeURIComponent(anchor)}`;
                }
            }

            console.log(`悬停预览API请求: ${apiUrl}`);
            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // 检查悬停窗口是否已关闭
            if (!targetEl.getAttribute('data-has-popover')) return;
            
            popoverEl.empty();
            const contentEl = popoverEl.createDiv('popover-content');
            contentEl.style.width = '100%';
            contentEl.style.boxSizing = 'border-box';
            
            await MarkdownRenderer.renderMarkdown(
                data.content,
                contentEl,
                '',
                plugin
            );
            
            // 处理所有内部链接 - 这里是关键部分
            contentEl.querySelectorAll('.internal-link').forEach(link => {
                // 移除未解析标记
                link.classList.remove('is-unresolved');
                
                // 解析链接文本，保留当前前缀作为默认值
                const linkText = link.textContent;
                const { matchPrefix, matchFilename } = parseLinkText(linkText, prefix);
                
                console.log(`处理悬停窗口中的链接: ${linkText}, 解析为前缀: ${matchPrefix}, 文件名: ${matchFilename}`);
                
                // 为链接添加点击和悬停事件
                link.addEventListener('click', (evt: MouseEvent) => {
                    evt.preventDefault();
                    evt.stopPropagation();
                    
                    // 查找对应的连接配置
                    const nestedConnection = plugin.settings.connections.find(conn => 
                        conn.description === matchPrefix
                    );
                    
                    if (nestedConnection) {
                        // 打开新的文件
                        handleLinkClick(plugin, evt, linkText, matchPrefix);
                    } else {
                        new Notice(`未找到连接配置: ${matchPrefix}`);
                    }
                });
                
                // 添加悬停预览事件
                link.addEventListener('mouseover', (evt: MouseEvent) => {
                    if (!evt.ctrlKey) return;
                    
                    // 查找对应的连接配置
                    const nestedConnection = plugin.settings.connections.find(conn => 
                        conn.description === matchPrefix
                    );
                    
                    if (nestedConnection) {
                        // 解析嵌套链接的锚点
                        const nestedParsed = plugin.parseCrossVaultLink(linkText || '');
                        const nestedAnchor = nestedParsed ? nestedParsed.anchor : '';

                        // 创建嵌套的悬停预览
                        createHoverPopover(
                            plugin,
                            link as HTMLElement,
                            evt,
                            matchPrefix,
                            matchFilename,
                            nestedConnection,
                            nestedAnchor
                        );
                    }
                });
            });
        } catch (error) {
            console.error('获取悬停预览内容失败:', error);
            
            // 检查悬停窗口是否已关闭
            if (!targetEl.getAttribute('data-has-popover')) return;
            
            popoverEl.empty();
            const errorEl = popoverEl.createDiv('popover-error');
            errorEl.setText('获取内容失败：' + error.message);
        }
    };
    
    // 加载内容
    loadContent();
    
    // 添加悬停窗口关闭时的清理
    popover.onunload = () => {
        targetEl.removeAttribute('data-has-popover');
        if (window.activeHoverPopover === popover) {
            window.activeHoverPopover = null;
            window.activeHoverPopoverTarget = null;
        }
    };
}

// 智能前缀隐藏功能 - 现在主要由 CodeMirror 装饰系统处理
export function applyCrossVaultLinkHiding(plugin: MyPlugin) {
    // console.log('[Connect Vault] 智能前缀隐藏现在由 CodeMirror 装饰系统处理');
    // console.log('[Connect Vault] 编辑器扩展应该已经自动处理跨库链接的前缀隐藏');

    // 这里可以添加一些额外的处理逻辑，但主要工作由装饰系统完成
}

// 简化版本完成

// 旧的复杂函数已删除，使用简化版本

// 调试函数 - 可以在控制台手动调用
export function debugCrossVaultLinks() {
    console.log('=== Connect Vault 调试信息 ===');

    // 检查所有内部链接
    const allInternalLinks = document.querySelectorAll('a.internal-link');
    console.log(`总共找到 ${allInternalLinks.length} 个 a.internal-link 元素`);

    allInternalLinks.forEach((link, index) => {
        const text = link.textContent;
        const isProcessed = link.hasAttribute('data-cross-vault-processed');
        const originalText = link.getAttribute('data-original-text');
        const prefix = link.getAttribute('data-prefix');
        const filename = link.getAttribute('data-filename');
        console.log(`  ${index + 1}. 当前显示: "${text}" - 已处理: ${isProcessed}`);
        if (originalText) {
            console.log(`      原始文本: "${originalText}", 前缀: "${prefix}", 文件名: "${filename}"`);
        }
    });

    // 检查编辑器链接
    const editorLinks = document.querySelectorAll('.cm-editor .cm-hmd-internal-link');
    console.log(`总共找到 ${editorLinks.length} 个编辑器内部链接`);

    editorLinks.forEach((link, index) => {
        const text = link.textContent;
        const isProcessed = link.hasAttribute('data-editor-processed');
        const originalText = link.getAttribute('data-original-text');
        const prefix = link.getAttribute('data-prefix');
        const filename = link.getAttribute('data-filename');
        console.log(`  ${index + 1}. 当前显示: "${text}" - 已处理: ${isProcessed}`);
        if (originalText) {
            console.log(`      原始文本: "${originalText}", 前缀: "${prefix}", 文件名: "${filename}"`);
        }
    });

    console.log('=== 调试信息结束 ===');
}

// 检查当前页面所有链接的函数
export function checkAllLinks() {
    console.log('=== 检查当前页面所有链接 ===');

    // 检查所有可能的链接选择器
    const selectors = [
        'a.internal-link',
        '.cm-hmd-internal-link',
        '.internal-link',
        '.cm-underline',
        'a[data-href]'
    ];

    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`${selector}: 找到 ${elements.length} 个元素`);

        elements.forEach((el, index) => {
            const text = el.textContent;
            const href = el.getAttribute('href') || el.getAttribute('data-href');
            console.log(`  ${index + 1}. 文本: "${text}", href: "${href}"`);
        });
    });

    console.log('=== 检查结束 ===');
}

// 手动触发智能前缀隐藏的函数
export function manualTriggerHiding() {
    console.log('=== 手动触发智能前缀隐藏 ===');

    // 获取插件实例（如果可用）
    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (plugin) {
        console.log('找到插件实例，开始处理...');
        applyCrossVaultLinkHiding(plugin);
    } else {
        console.log('未找到插件实例');
    }
}

// 测试新实现的函数
export function testNewImplementation() {
    console.log('=== 测试新的智能前缀隐藏实现 ===');

    // 检查 Markdown 后处理器是否工作
    const readingLinks = document.querySelectorAll('a.internal-link[data-cross-vault-processed="true"]');
    console.log(`阅读模式处理的链接数量: ${readingLinks.length}`);

    readingLinks.forEach((link, index) => {
        const prefix = link.getAttribute('data-prefix');
        const filename = link.getAttribute('data-filename');
        const currentText = link.textContent;
        console.log(`  ${index + 1}. 前缀: "${prefix}", 文件名: "${filename}", 当前显示: "${currentText}"`);
    });

    // 检查编辑模式处理的链接
    const editorLinks = document.querySelectorAll('.cm-hmd-internal-link[data-editor-processed="true"]');
    console.log(`编辑模式处理的链接数量: ${editorLinks.length}`);

    editorLinks.forEach((link, index) => {
        const prefix = link.getAttribute('data-prefix');
        const filename = link.getAttribute('data-filename');
        const currentText = link.textContent;
        console.log(`  ${index + 1}. 前缀: "${prefix}", 文件名: "${filename}", 当前显示: "${currentText}"`);
    });

    console.log('=== 测试完成 ===');
}

// 强制刷新所有智能链接
export function forceRefreshSmartLinks() {
    console.log('=== 强制刷新所有智能链接 ===');

    // 清理所有已处理的链接
    document.querySelectorAll('[data-cross-vault-processed], [data-editor-processed]').forEach(link => {
        const linkEl = link as HTMLElement;

        // 清理观察器
        const observer = (linkEl as any)._crossVaultObserver;
        if (observer) {
            observer.disconnect();
            delete (linkEl as any)._crossVaultObserver;
        }

        // 清理事件处理器
        const handlers = (linkEl as any)._crossVaultHandlers;
        if (handlers) {
            linkEl.removeEventListener('mouseenter', handlers.mouseEnter);
            linkEl.removeEventListener('mouseleave', handlers.mouseLeave);
            delete (linkEl as any)._crossVaultHandlers;
        }

        // 清理类和属性
        linkEl.classList.remove('cross-vault-smart-link', 'show-full-text');
        linkEl.removeAttribute('data-cross-vault-processed');
        linkEl.removeAttribute('data-editor-processed');
        linkEl.removeAttribute('data-display-text');

        console.log(`清理链接: ${linkEl.textContent}`);
    });

    // 重新应用智能隐藏
    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (plugin) {
        setTimeout(() => {
            applyCrossVaultLinkHiding(plugin);
        }, 500);
    }

    console.log('=== 刷新完成 ===');
}

// 测试 CodeMirror 装饰系统
export function testCodeMirrorDecorations() {
    console.log('=== 测试 CodeMirror 装饰系统 ===');

    // 查找所有编辑器
    const editors = document.querySelectorAll('.cm-editor');
    console.log(`找到 ${editors.length} 个编辑器`);

    editors.forEach((editor, index) => {
        console.log(`编辑器 ${index + 1}:`);

        // 查找编辑器中的链接
        const links = editor.querySelectorAll('.cm-hmd-internal-link');
        console.log(`  包含 ${links.length} 个内部链接`);

        links.forEach((link, linkIndex) => {
            const text = link.textContent;
            console.log(`    链接 ${linkIndex + 1}: "${text}"`);

            // 检查是否是跨库链接
            if (text && text.includes(':')) {
                const match = text.match(/^(.*?):(.*?)$/);
                if (match) {
                    const [_, prefix, filename] = match;
                    console.log(`      跨库链接: 前缀="${prefix}", 文件名="${filename}"`);
                    console.log(`      当前显示的文本: "${text}"`);

                    // 检查是否有装饰隐藏了前缀
                    const computedStyle = window.getComputedStyle(link);
                    console.log(`      计算样式: display=${computedStyle.display}, visibility=${computedStyle.visibility}`);
                }
            }
        });
    });

    console.log('=== 测试完成 ===');
}

// 手动触发后处理器测试
export function manualTriggerPostProcessor() {
    console.log('=== 手动触发后处理器测试 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 查找所有可能需要后处理的元素
    const elements = document.querySelectorAll('.markdown-preview-view, .markdown-rendered');
    console.log(`找到 ${elements.length} 个可能的容器元素`);

    elements.forEach((element, index) => {
        console.log(`处理容器 ${index + 1}:`, element.className);
        plugin.markdownPostProcessor(element, {});
    });

    console.log('=== 手动触发完成 ===');
}

// 检查插件状态
export function checkPluginStatus() {
    console.log('=== 检查插件状态 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (plugin) {
        console.log('插件实例:', plugin);
        console.log('插件设置:', plugin.settings);
        console.log('连接数量:', plugin.settings.connections.length);
        console.log('编辑器扩展:', plugin.editorExtension);
        console.log('后处理器:', plugin.markdownPostProcessor);
    } else {
        console.log('未找到插件实例');
    }

    console.log('=== 状态检查完成 ===');
}

// 测试 API 接口
export function testApiEndpoint() {
    console.log('=== 测试 API 接口 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    const port = plugin.settings.port;
    const baseUrl = `http://127.0.0.1:${port}`;

    console.log(`API 服务器地址: ${baseUrl}`);

    // 测试基本的笔记获取
    testApiCall(`${baseUrl}/api/notes`, '获取笔记列表');

    // 如果有连接配置，测试跨库 API
    if (plugin.settings.connections.length > 0) {
        const connection = plugin.settings.connections[0];
        const crossVaultUrl = `http://127.0.0.1:${connection.port}`;

        console.log(`测试跨库连接: ${connection.description} (${crossVaultUrl})`);

        testApiCall(`${crossVaultUrl}/api/notes`, `获取 ${connection.description} 库的笔记列表`);

        // 测试获取特定笔记
        testApiCall(`${crossVaultUrl}/api/note?name=README`, `获取 README 文件`);

        // 测试章节参数
        testApiCall(`${crossVaultUrl}/api/note?name=README&head=安装`, `获取 README 的安装章节`);

        // 测试块参数
        testApiCall(`${crossVaultUrl}/api/note?name=README&id=example`, `获取 README 的 example 块`);
    }

    console.log('=== API 测试完成 ===');
}

async function testApiCall(url: string, description: string) {
    try {
        console.log(`\n测试: ${description}`);
        console.log(`URL: ${url}`);

        const response = await fetch(url);
        console.log(`状态: ${response.status} ${response.statusText}`);

        if (response.ok) {
            const data = await response.json();
            console.log(`响应:`, data);

            if (Array.isArray(data)) {
                console.log(`返回 ${data.length} 个项目`);
            } else if (data.content) {
                console.log(`内容长度: ${data.content.length} 字符`);
                console.log(`内容类型: ${data.contentType || 'full'}`);
                if (data.target) {
                    console.log(`目标: ${data.target}`);
                }
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            console.log(`错误:`, errorData);
        }

    } catch (error) {
        console.error(`请求失败:`, error.message);
    }
}

// 测试 Obsidian 内置预览功能
export function testObsidianPreview() {
    console.log('=== 测试 Obsidian 内置预览功能 ===');

    // 查找跨库链接
    const crossVaultLinks = document.querySelectorAll('.cm-hmd-internal-link, .internal-link');
    console.log(`找到 ${crossVaultLinks.length} 个链接`);

    let crossVaultCount = 0;
    crossVaultLinks.forEach((link, index) => {
        const text = link.textContent;
        if (text && text.includes(':')) {
            crossVaultCount++;
            console.log(`跨库链接 ${crossVaultCount}: "${text}"`);

            // 检查链接是否被正确识别
            const href = link.getAttribute('href') || link.getAttribute('data-href');
            console.log(`  href: ${href}`);

            // 检查是否有 Obsidian 的内置预览类
            const classes = Array.from(link.classList);
            console.log(`  classes: ${classes.join(', ')}`);
        }
    });

    if (crossVaultCount === 0) {
        console.log('没有找到跨库链接，请先创建一个 [[库前缀:文件名]] 格式的链接');
    } else {
        console.log('\n现在请手动将鼠标悬停在跨库链接上，查看 Obsidian 内置预览是否正常工作');
    }

    console.log('=== 测试完成 ===');
}

// 测试章节链接解析
export function testHeadingLinkParsing() {
    console.log('=== 测试章节链接解析 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 测试不同格式的链接
    const testLinks = [
        'market:aa',
        'market:aa#A1',
        'market:aa#^block1',
        'market:用户手册#安装指南'
    ];

    testLinks.forEach(linkpath => {
        console.log(`\n测试链接: ${linkpath}`);

        // 测试 getFirstLinkpathDest
        const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(linkpath, '');
        if (fileObj) {
            console.log('  文件对象:', {
                path: fileObj.path,
                name: fileObj.name,
                basename: fileObj.basename,
                anchor: fileObj.anchor,
                isRemoteFile: fileObj.isRemoteFile
            });

            // 测试 getFileCache
            const cache = plugin.app.metadataCache.getFileCache(fileObj);
            if (cache) {
                console.log('  缓存对象:', {
                    headings: cache.headings,
                    hash: cache.hash
                });
            }
        } else {
            console.log('  未找到文件对象');
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 测试实际的预览功能
export function testActualPreview() {
    console.log('=== 测试实际预览功能 ===');

    // 创建一个测试链接元素
    const testLink = document.createElement('a');
    testLink.className = 'internal-link';
    testLink.textContent = 'market:aa#A1';
    testLink.href = 'market:aa#A1';
    testLink.style.cssText = 'position: fixed; top: 100px; left: 100px; z-index: 1000; background: yellow; padding: 10px;';

    document.body.appendChild(testLink);

    console.log('已创建测试链接，请将鼠标悬停在黄色链接上测试预览功能');

    // 10秒后移除测试链接
    setTimeout(() => {
        testLink.remove();
        console.log('测试链接已移除');
    }, 10000);
}

// 测试远程文件缓存系统
export function testRemoteFileCache() {
    console.log('=== 测试远程文件缓存系统 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 测试不同类型的链接
    const testLinks = [
        'market:aa',
        'market:aa#A1',
        'market:aa#^block1'
    ];

    testLinks.forEach(async (linkpath) => {
        console.log(`\n测试缓存: ${linkpath}`);

        // 获取文件对象
        const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(linkpath, '');
        if (fileObj) {
            console.log('文件对象创建成功:', fileObj.path);

            // 获取缓存
            const cache = plugin.app.metadataCache.getFileCache(fileObj);
            console.log('缓存对象:', {
                headings: cache?.headings?.length || 0,
                blocks: Object.keys(cache?.blocks || {}).length,
                hash: cache?.hash
            });

            // 检查远程缓存映射
            if (plugin.remoteCacheMap.has(linkpath)) {
                console.log('远程缓存已存在');
            } else {
                console.log('远程缓存不存在，等待异步创建...');

                // 等待一段时间后再检查
                setTimeout(() => {
                    if (plugin.remoteCacheMap.has(linkpath)) {
                        const remoteCache = plugin.remoteCacheMap.get(linkpath);
                        console.log(`远程缓存已创建: ${remoteCache.headings?.length || 0} 个标题, ${Object.keys(remoteCache.blocks || {}).length} 个块`);
                    } else {
                        console.log('远程缓存创建失败或仍在进行中');
                    }
                }, 2000);
            }
        } else {
            console.log('文件对象创建失败');
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 清理远程缓存
export function clearRemoteCache() {
    console.log('=== 清理远程缓存 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    const cacheSize = plugin.remoteCacheMap.size;
    plugin.remoteCacheMap.clear();

    console.log(`已清理 ${cacheSize} 个远程缓存条目`);
}

// 测试跨库跳转功能
export function testCrossVaultNavigation() {
    console.log('=== 测试跨库跳转功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 测试不同类型的跳转
    const testLinks = [
        'market:aa',
        'market:aa#A1',
        'market:aa#^block1'
    ];

    testLinks.forEach((linktext, index) => {
        console.log(`\n测试跳转 ${index + 1}: ${linktext}`);

        // 模拟点击链接
        setTimeout(() => {
            console.log(`模拟打开链接: ${linktext}`);

            try {
                // 直接调用 openLinkText 方法
                plugin.app.workspace.openLinkText(linktext, '', false);
                console.log(`跳转请求已发送: ${linktext}`);
            } catch (error) {
                console.error(`跳转失败: ${error.message}`);
            }
        }, index * 1000); // 每个链接间隔1秒
    });

    console.log('\n注意: 跳转需要目标库已经在 Obsidian 中打开');
    console.log('=== 测试完成 ===');
}

// 手动测试单个链接跳转
export function testSingleNavigation(linktext: string) {
    console.log(`=== 测试单个链接跳转: ${linktext} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    try {
        plugin.app.workspace.openLinkText(linktext, '', false);
        console.log(`跳转请求已发送: ${linktext}`);
    } catch (error) {
        console.error(`跳转失败: ${error.message}`);
    }
}

// 测试不同的 URI 格式
export function testUriFormats() {
    console.log('=== 测试不同的 URI 格式 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    if (plugin.settings.connections.length === 0) {
        console.log('没有配置的连接');
        return;
    }

    const connection = plugin.settings.connections[0];
    const vaultName = connection.vaultName;
    const filename = 'aa';
    const heading = 'A1';
    const blockId = 'block1';

    // 测试不同的 URI 格式
    const testUris = [
        // 基本文件
        `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename)}`,

        // 章节 - 方法1: 文件名中包含锚点
        `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename + '#' + heading)}`,

        // 章节 - 方法2: 单独的 heading 参数
        `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename)}&heading=${encodeURIComponent(heading)}`,

        // 块 - 方法1: 文件名中包含锚点
        `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename + '#^' + blockId)}`,

        // 块 - 方法2: 单独的 block 参数
        `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filename)}&block=${encodeURIComponent('^' + blockId)}`,
    ];

    testUris.forEach((uri, index) => {
        console.log(`\n测试 URI ${index + 1}:`);
        console.log(uri);

        // 延迟执行，避免同时打开多个
        setTimeout(() => {
            console.log(`打开 URI ${index + 1}`);
            window.open(uri);
        }, index * 2000); // 每个 URI 间隔2秒
    });

    console.log('\n=== URI 测试完成 ===');
    console.log('请观察哪种格式能正确跳转到章节/块位置');
}

// 测试内部链接转换功能
export function testLinkTransformation() {
    console.log('=== 测试内部链接转换功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 测试内容
    const testContent = `# 测试文档

这是一个包含多种链接的测试文档：

1. 普通内部链接：[[aaa]]
2. 带别名的链接：[[bbb|显示名称]]
3. 章节链接：[[ccc#章节1]]
4. 块链接：[[ddd#^block1]]
5. 嵌入链接：![[eee]]
6. 带别名的嵌入：![[fff|图片]]
7. 已有跨库链接：[[other:ggg]]（应该保持不变）
8. 当前文件内链接：[[#本文档章节]]（应该保持不变）

## 章节2

更多内容...`;

    console.log('\n原始内容:');
    console.log(testContent);

    // 调用转换函数
    const transformedContent = plugin.transformInternalLinks(testContent, 'market');

    console.log('\n转换后内容:');
    console.log(transformedContent);

    console.log('\n=== 转换测试完成 ===');
}

// 测试实际的远程文件内容转换
export function testRemoteContentTransformation() {
    console.log('=== 测试远程文件内容转换 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    if (plugin.settings.connections.length === 0) {
        console.log('没有配置的连接');
        return;
    }

    const connection = plugin.settings.connections[0];
    const testFile = 'aa'; // 假设这个文件存在并包含内部链接

    console.log(`测试文件: ${connection.description}:${testFile}`);

    // 模拟 cachedRead 调用
    const testPath = `${connection.description}:${testFile}`;

    // 创建虚拟文件对象
    const virtualFile = {
        path: testPath,
        name: testFile + '.md',
        basename: testFile,
        isRemoteFile: true
    };

    console.log('调用 cachedRead 获取转换后的内容...');

    plugin.app.vault.cachedRead(virtualFile)
        .then((content: string) => {
            console.log('\n获取到的转换后内容:');
            console.log(content);
            console.log('\n检查内容中是否包含跨库链接格式...');

            // 检查是否有转换后的链接
            const crossVaultLinks = content.match(/\[\[[^:]+:[^\]]+\]\]/g);
            if (crossVaultLinks) {
                console.log(`找到 ${crossVaultLinks.length} 个跨库链接:`);
                crossVaultLinks.forEach((link, index) => {
                    console.log(`  ${index + 1}. ${link}`);
                });
            } else {
                console.log('没有找到跨库链接，可能原文件不包含内部链接');
            }
        })
        .catch((error: Error) => {
            console.error('获取远程内容失败:', error.message);
        });

    console.log('=== 远程内容转换测试完成 ===');
}

// 测试编辑 API
export function testEditApi() {
    console.log('=== 测试编辑 API ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    if (plugin.settings.connections.length === 0) {
        console.log('没有配置的连接');
        return;
    }

    const connection = plugin.settings.connections[0];
    const baseUrl = `http://127.0.0.1:${connection.port}`;

    // 测试编辑整个文件
    testEditFile(baseUrl, 'test-edit', '# 测试编辑\n\n这是通过 API 编辑的内容。\n\n## 章节1\n\n内容1 ^block1\n\n## 章节2\n\n内容2');

    // 延迟测试章节编辑
    setTimeout(() => {
        testEditHeading(baseUrl, 'test-edit', '章节1', '## 章节1\n\n这是修改后的章节1内容 ^block1');
    }, 2000);

    // 延迟测试块编辑
    setTimeout(() => {
        testEditBlock(baseUrl, 'test-edit', 'block1', '这是修改后的块内容');
    }, 4000);

    console.log('=== 编辑 API 测试完成 ===');
}

// 测试编辑整个文件
async function testEditFile(baseUrl: string, fileName: string, content: string) {
    console.log(`\n测试编辑整个文件: ${fileName}`);

    try {
        const response = await fetch(`${baseUrl}/api/note?name=${fileName}&edit=true`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content })
        });

        const result = await response.json();

        if (response.ok) {
            console.log('文件编辑成功:', result);
        } else {
            console.error('文件编辑失败:', result);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 测试编辑章节
async function testEditHeading(baseUrl: string, fileName: string, heading: string, content: string) {
    console.log(`\n测试编辑章节: ${fileName}#${heading}`);

    try {
        const response = await fetch(`${baseUrl}/api/note?name=${fileName}&head=${encodeURIComponent(heading)}&edit=true`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content })
        });

        const result = await response.json();

        if (response.ok) {
            console.log('章节编辑成功:', result);
        } else {
            console.error('章节编辑失败:', result);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 测试编辑块
async function testEditBlock(baseUrl: string, fileName: string, blockId: string, content: string) {
    console.log(`\n测试编辑块: ${fileName}#^${blockId}`);

    try {
        const response = await fetch(`${baseUrl}/api/note?name=${fileName}&id=${blockId}&edit=true`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ content })
        });

        const result = await response.json();

        if (response.ok) {
            console.log('块编辑成功:', result);
        } else {
            console.error('块编辑失败:', result);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 手动测试编辑 API
export function manualEditTest(fileName: string, content: string, heading?: string, blockId?: string) {
    console.log(`=== 手动编辑测试: ${fileName} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    if (plugin.settings.connections.length === 0) {
        console.log('没有配置的连接');
        return;
    }

    const connection = plugin.settings.connections[0];
    const baseUrl = `http://127.0.0.1:${connection.port}`;

    let url = `${baseUrl}/api/note?name=${fileName}&edit=true`;
    if (heading) {
        url += `&head=${encodeURIComponent(heading)}`;
    } else if (blockId) {
        url += `&id=${blockId}`;
    }

    console.log(`请求 URL: ${url}`);
    console.log(`请求内容: ${content}`);

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ content })
    })
    .then(response => response.json())
    .then(result => {
        console.log('编辑结果:', result);
    })
    .catch(error => {
        console.error('编辑失败:', error);
    });
}

// 测试跨库编辑功能
export function testCrossVaultEdit() {
    console.log('=== 测试跨库编辑功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 测试不同类型的跨库编辑
    const testCases = [
        {
            linktext: 'market:test-edit',
            description: '编辑整个文件'
        },
        {
            linktext: 'market:aa#A1',
            description: '编辑特定章节'
        },
        {
            linktext: 'market:aa#^block1',
            description: '编辑特定块'
        }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`\n测试 ${index + 1}: ${testCase.description}`);
        console.log(`链接: ${testCase.linktext}`);

        // 获取虚拟文件对象
        const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(testCase.linktext, '');
        if (fileObj) {
            console.log('虚拟文件对象:', {
                path: fileObj.path,
                name: fileObj.name,
                isRemoteFile: fileObj.isRemoteFile,
                anchor: fileObj.anchor
            });

            // 模拟编辑操作
            setTimeout(() => {
                console.log(`模拟编辑 ${testCase.linktext}...`);
                const testContent = `# 测试编辑 ${index + 1}\n\n这是通过跨库编辑功能修改的内容。\n\n时间: ${new Date().toLocaleString()}`;

                // 调用 vault.modify 方法
                plugin.app.vault.modify(fileObj, testContent)
                    .then(() => {
                        console.log(`编辑成功: ${testCase.linktext}`);
                    })
                    .catch((error: Error) => {
                        console.error(`编辑失败: ${testCase.linktext}`, error.message);
                    });
            }, (index + 1) * 2000); // 每个测试间隔2秒
        } else {
            console.log('无法创建虚拟文件对象');
        }
    });

    console.log('\n=== 跨库编辑测试完成 ===');
    console.log('请观察控制台输出和通知消息');
}

// 手动测试单个跨库编辑
export function testSingleCrossVaultEdit(linktext: string, content: string) {
    console.log(`=== 手动测试跨库编辑: ${linktext} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    // 获取虚拟文件对象
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(linktext, '');
    if (!fileObj) {
        console.log('无法创建虚拟文件对象');
        return;
    }

    console.log('虚拟文件对象:', fileObj);
    console.log('编辑内容:', content);

    // 执行编辑
    plugin.app.vault.modify(fileObj, content)
        .then(() => {
            console.log('编辑成功!');
        })
        .catch((error: Error) => {
            console.error('编辑失败:', error.message);
        });
}

// 测试打开跨库文件进行编辑
export function testOpenCrossVaultFile() {
    console.log('=== 测试打开跨库文件进行编辑 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    const testLink = 'market:aa';
    console.log(`尝试打开跨库文件: ${testLink}`);

    // 尝试在新标签页中打开跨库文件
    plugin.app.workspace.openLinkText(testLink, '', true)
        .then(() => {
            console.log('文件打开成功，您现在可以在编辑器中直接编辑');
            console.log('编辑后保存时，更改将通过 API 同步到远程文件');
        })
        .catch((error: Error) => {
            console.error('文件打开失败:', error.message);
        });
}

// 调试块引用问题
export function debugBlockReference() {
    console.log('=== 调试块引用问题 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('未找到插件实例');
        return;
    }

    if (plugin.settings.connections.length === 0) {
        console.log('没有配置的连接');
        return;
    }

    const connection = plugin.settings.connections[0];
    const testLink = `${connection.description}:aa#^1`;

    console.log(`测试块引用链接: ${testLink}`);

    // 1. 测试链接解析
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(testLink, '');
    if (fileObj) {
        console.log('虚拟文件对象:', {
            path: fileObj.path,
            name: fileObj.name,
            anchor: fileObj.anchor
        });
    }

    // 2. 测试 cachedRead
    if (fileObj) {
        console.log('\n测试 cachedRead...');
        plugin.app.vault.cachedRead(fileObj)
            .then((content: string) => {
                console.log('获取到的内容:', content);
                console.log('内容长度:', content.length);
                if (content.length === 0) {
                    console.log('❌ 内容为空！这就是问题所在');
                } else {
                    console.log('✅ 内容正常');
                }
            })
            .catch((error: Error) => {
                console.error('cachedRead 失败:', error.message);
            });
    }

    // 3. 直接测试 API
    const apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=aa&id=1`;
    console.log(`\n直接测试 API: ${apiUrl}`);

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            console.log('API 响应:', data);
            if (data.error) {
                console.log('❌ API 返回错误:', data.error);
            } else {
                console.log('✅ API 响应正常');
                console.log('内容:', data.content);
            }
        })
        .catch(error => {
            console.error('API 请求失败:', error);
        });

    console.log('=== 调试完成 ===');
}

// 专门测试块引用预览
export function testBlockReferencePreview() {
    console.log('=== 测试块引用预览 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin || plugin.settings.connections.length === 0) {
        console.log('插件或连接配置不可用');
        return;
    }

    const connection = plugin.settings.connections[0];

    // 测试不同的块引用格式
    const testLinks = [
        `${connection.description}:aa#^1`,
        `${connection.description}:aa#^block1`,
        `${connection.description}:aa#^test-block`
    ];

    testLinks.forEach((testLink, index) => {
        console.log(`\n--- 测试 ${index + 1}: ${testLink} ---`);

        // 解析链接
        const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(testLink, '');
        if (fileObj) {
            console.log('文件对象:', {
                path: fileObj.path,
                anchor: fileObj.anchor
            });

            // 测试内容获取
            plugin.app.vault.cachedRead(fileObj)
                .then((content: string) => {
                    console.log(`内容长度: ${content.length}`);
                    if (content.length > 0) {
                        console.log(`内容预览: "${content.substring(0, 100)}..."`);
                    } else {
                        console.log('❌ 内容为空');
                    }
                })
                .catch((error: Error) => {
                    console.error('获取内容失败:', error.message);
                });
        } else {
            console.log('❌ 无法创建文件对象');
        }
    });
}

// 测试不同的块ID格式
export function testBlockIdFormats() {
    console.log('=== 测试不同的块ID格式 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin || plugin.settings.connections.length === 0) {
        console.log('插件或连接配置不可用');
        return;
    }

    const connection = plugin.settings.connections[0];
    const baseUrl = `http://127.0.0.1:${connection.port}`;

    // 测试不同的块ID格式
    const testCases = [
        { id: '1', description: '数字块ID' },
        { id: 'block1', description: '字母数字块ID' },
        { id: 'test-block', description: '带连字符的块ID' }
    ];

    testCases.forEach((testCase, index) => {
        setTimeout(() => {
            const apiUrl = `${baseUrl}/api/note?name=aa&id=${testCase.id}`;
            console.log(`\n测试 ${index + 1}: ${testCase.description}`);
            console.log(`API URL: ${apiUrl}`);

            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    console.log(`结果:`, data);
                })
                .catch(error => {
                    console.error(`失败:`, error);
                });
        }, index * 1000);
    });
}

// 测试新的锚点解析方法
export function testAnchorResolution() {
    console.log('=== 测试锚点解析方法 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin || plugin.settings.connections.length === 0) {
        console.log('插件或连接配置不可用');
        return;
    }

    const connection = plugin.settings.connections[0];

    // 清理缓存
    const linkCache = (window as any)._crossVaultLinkCache;
    if (linkCache) {
        linkCache.clear();
        console.log('已清理链接缓存');
    }

    // 测试链接
    const testLink = `${connection.description}:aa#^1`;
    console.log(`测试链接: ${testLink}`);

    // 手动添加到缓存（模拟从文档中找到）
    if (linkCache) {
        linkCache.set(`${connection.description}:aa`, '^1');
        console.log('已手动添加锚点到缓存');
    }

    // 测试解析
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${connection.description}:aa`, '');
    if (fileObj) {
        console.log('解析结果:', {
            path: fileObj.path,
            anchor: fileObj.anchor
        });

        if (fileObj.anchor === '^1') {
            console.log('✅ 锚点解析成功！');
        } else {
            console.log('❌ 锚点解析失败');
        }
    } else {
        console.log('❌ 无法创建文件对象');
    }
}

// 检查当前文档中的跨库链接（仅显示，不解析）
export function scanCurrentDocumentLinks() {
    console.log('=== 扫描当前文档中的跨库链接 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    plugin.scanCurrentDocumentForDebug();
    console.log('=== 扫描完成 ===');
}

// 完整的块引用调试流程
export function debugBlockReferenceComplete() {
    console.log('=== 完整的块引用调试流程 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin || plugin.settings.connections.length === 0) {
        console.log('❌ 插件或连接配置不可用');
        return;
    }

    const connection = plugin.settings.connections[0];
    const testLink = `${connection.description}:aa#^1`;

    console.log(`🔍 测试链接: ${testLink}`);

    // 步骤1: 检查缓存状态
    const linkCache = (window as any)._crossVaultLinkCache;
    console.log('\n📋 步骤1: 检查缓存状态');
    if (linkCache) {
        console.log(`缓存大小: ${linkCache.size}`);
        console.log('缓存内容:');
        for (const [key, value] of linkCache.entries()) {
            console.log(`  ${key} -> ${value}`);
        }
    } else {
        console.log('❌ 缓存不存在');
    }

    // 步骤2: 扫描当前文档
    console.log('\n📄 步骤2: 扫描当前文档');
    scanCurrentDocumentLinks();

    // 步骤3: 再次检查缓存
    console.log('\n📋 步骤3: 扫描后的缓存状态');
    if (linkCache) {
        console.log(`缓存大小: ${linkCache.size}`);
        for (const [key, value] of linkCache.entries()) {
            console.log(`  ${key} -> ${value}`);
        }
    }

    // 步骤4: 测试链接解析
    console.log('\n🔗 步骤4: 测试链接解析');
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${connection.description}:aa`, '');
    if (fileObj) {
        console.log('文件对象创建成功:');
        console.log(`  路径: ${fileObj.path}`);
        console.log(`  锚点: ${fileObj.anchor}`);
        console.log(`  是否远程文件: ${fileObj.isRemoteFile}`);

        // 步骤5: 测试内容获取
        console.log('\n📖 步骤5: 测试内容获取');
        plugin.app.vault.cachedRead(fileObj)
            .then((content: string) => {
                console.log(`✅ 内容获取成功: ${content.length} 字符`);
                if (content.length > 0) {
                    console.log(`内容预览: "${content.substring(0, 100)}..."`);
                } else {
                    console.log('❌ 内容为空');
                }
            })
            .catch((error: Error) => {
                console.error('❌ 内容获取失败:', error.message);
            });
    } else {
        console.log('❌ 文件对象创建失败');
    }

    // 步骤6: 直接测试API
    console.log('\n🌐 步骤6: 直接测试API');
    const apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=aa&id=1`;
    console.log(`API URL: ${apiUrl}`);

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            console.log('API 响应:', data);
            if (data.error) {
                console.log('❌ API 返回错误:', data.error);
            } else {
                console.log('✅ API 响应正常');
                console.log(`内容: "${data.content}"`);
            }
        })
        .catch(error => {
            console.error('❌ API 请求失败:', error);
        });

    console.log('\n=== 调试流程完成 ===');
}

// 强制刷新链接缓存
export function forceRefreshLinkCache() {
    console.log('=== 强制刷新链接缓存 ===');

    const linkCache = (window as any)._crossVaultLinkCache;
    if (linkCache) {
        linkCache.clear();
        console.log('已清空缓存');
    }

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (plugin) {
        plugin.scanAndCacheCurrentDocumentLinks();
        console.log('已重新扫描文档');
    }

    console.log('=== 刷新完成 ===');
}

// 专门测试特定块ID
export function testSpecificBlockId(prefix: string, filename: string, blockId: string) {
    console.log(`=== 测试特定块ID: ${prefix}:${filename}#^${blockId} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    const connection = plugin.settings.connections.find((conn: any) => conn.description === prefix);
    if (!connection) {
        console.log(`❌ 未找到前缀 "${prefix}" 对应的连接配置`);
        return;
    }

    // 1. 测试API直接调用
    const apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}&id=${encodeURIComponent(blockId)}`;
    console.log(`\n🌐 步骤1: 直接测试API`);
    console.log(`API URL: ${apiUrl}`);

    fetch(apiUrl)
        .then(response => {
            console.log(`响应状态: ${response.status} ${response.statusText}`);
            return response.json();
        })
        .then(data => {
            console.log('API 响应数据:', data);
            if (data.error) {
                console.log(`❌ API 错误: ${data.error}`);
            } else {
                console.log(`✅ API 成功: 内容长度 ${data.content?.length || 0}`);
                if (data.content) {
                    console.log(`内容预览: "${data.content.substring(0, 100)}"`);
                }
            }
        })
        .catch(error => {
            console.error(`❌ API 请求失败:`, error);
        });

    // 2. 测试完整文件内容，查看块标记
    const fullFileUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}`;
    console.log(`\n📄 步骤2: 获取完整文件内容`);
    console.log(`完整文件 URL: ${fullFileUrl}`);

    fetch(fullFileUrl)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.log(`❌ 获取完整文件失败: ${data.error}`);
            } else {
                console.log(`✅ 完整文件获取成功: ${data.content.length} 字符`);

                // 分析文件中的所有块标记
                const content = data.content;
                const lines = content.split('\n');
                const blockMarkers: Array<{line: number, content: string, markers: string[]}> = [];

                lines.forEach((line: string, index: number) => {
                    if (line.includes('^')) {
                        blockMarkers.push({
                            line: index + 1,
                            content: line,
                            markers: line.match(/\^[a-zA-Z0-9-_]+/g) || []
                        });
                    }
                });

                console.log(`\n🔍 文件中的所有块标记:`);
                if (blockMarkers.length === 0) {
                    console.log('❌ 文件中没有找到任何块标记');
                } else {
                    blockMarkers.forEach((marker: any) => {
                        console.log(`  第${marker.line}行: "${marker.content}"`);
                        console.log(`    块标记: ${marker.markers.join(', ')}`);

                        // 检查是否包含我们要找的块ID
                        if (marker.markers.includes(`^${blockId}`)) {
                            console.log(`    ✅ 找到目标块ID: ^${blockId}`);
                        }
                    });
                }

                // 特别检查目标块ID
                if (content.includes(`^${blockId}`)) {
                    console.log(`\n✅ 文件中确实包含块ID: ^${blockId}`);
                    const regex = new RegExp(`^.*\\^${blockId}.*$`, 'gm');
                    const matches = content.match(regex);
                    if (matches) {
                        console.log(`匹配的行:`);
                        matches.forEach((match: string) => {
                            console.log(`  "${match}"`);
                        });
                    }
                } else {
                    console.log(`\n❌ 文件中不包含块ID: ^${blockId}`);
                }
            }
        })
        .catch(error => {
            console.error(`❌ 获取完整文件失败:`, error);
        });

    // 3. 测试插件的链接解析
    console.log(`\n🔗 步骤3: 测试插件链接解析`);
    const fullLinkPath = `${prefix}:${filename}#^${blockId}`;

    // 手动添加到缓存
    const linkCache = (window as any)._crossVaultLinkCache;
    if (linkCache) {
        linkCache.set(`${prefix}:${filename}`, `^${blockId}`);
        console.log(`已手动添加到缓存: ${prefix}:${filename} -> ^${blockId}`);
    }

    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${prefix}:${filename}`, '');
    if (fileObj) {
        console.log(`文件对象创建成功:`);
        console.log(`  路径: ${fileObj.path}`);
        console.log(`  锚点: ${fileObj.anchor}`);

        // 测试内容获取
        plugin.app.vault.cachedRead(fileObj)
            .then((content: string) => {
                console.log(`✅ 通过插件获取内容成功: ${content.length} 字符`);
                if (content.length > 0) {
                    console.log(`内容: "${content}"`);
                } else {
                    console.log(`❌ 内容为空`);
                }
            })
            .catch((error: Error) => {
                console.error(`❌ 通过插件获取内容失败:`, error.message);
            });
    } else {
        console.log(`❌ 文件对象创建失败`);
    }

    console.log(`\n=== 测试完成 ===`);
}

// 测试新的链接解析逻辑
export function testNewLinkParsing() {
    console.log('=== 测试新的链接解析逻辑 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 测试不同格式的链接
    const testLinks = [
        'test:aa',           // 基本文件
        'test:aa#A1',        // 章节
        'test:aa#^1',        // 块引用
        'market:file#^block1', // 另一个块引用
        'invalid:link'       // 无效链接
    ];

    testLinks.forEach((linkText, index) => {
        console.log(`\n--- 测试 ${index + 1}: "${linkText}" ---`);

        const parsed = plugin.parseCrossVaultLink(linkText);
        if (parsed) {
            console.log(`✅ 解析成功:`);
            console.log(`  前缀: "${parsed.prefix}"`);
            console.log(`  文件名: "${parsed.filename}"`);
            console.log(`  锚点: "${parsed.anchor}"`);
            console.log(`  类型: ${parsed.anchorType}`);

            // 测试实际的链接处理
            if (parsed.anchorType !== 'none') {
                console.log(`\n🔗 测试链接处理:`);

                // 手动添加到缓存
                const linkCache = (window as any)._crossVaultLinkCache;
                if (linkCache) {
                    const cacheKey = `${parsed.prefix}:${parsed.filename}`;
                    linkCache.set(cacheKey, parsed.anchor);
                    console.log(`  已添加到缓存: ${cacheKey} -> ${parsed.anchor}`);
                }

                // 测试文件对象创建
                const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${parsed.prefix}:${parsed.filename}`, '');
                if (fileObj) {
                    console.log(`  ✅ 文件对象创建成功:`);
                    console.log(`    路径: ${fileObj.path}`);
                    console.log(`    锚点: ${fileObj.anchor}`);

                    // 测试内容获取
                    plugin.app.vault.cachedRead(fileObj)
                        .then((content: string) => {
                            console.log(`    ✅ 内容获取成功: ${content.length} 字符`);
                            if (content.length === 0) {
                                console.log(`    ❌ 但内容为空！`);
                            }
                        })
                        .catch((error: Error) => {
                            console.log(`    ❌ 内容获取失败: ${error.message}`);
                        });
                } else {
                    console.log(`  ❌ 文件对象创建失败`);
                }
            }
        } else {
            console.log(`❌ 解析失败或不是跨库链接`);
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 测试精确链接查找
export function testPreciseLinkFinding() {
    console.log('=== 测试精确链接查找 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 测试查找特定链接
    const testCases = [
        { prefix: 'test', filename: 'aa' },
        { prefix: 'market', filename: 'file' },
        { prefix: 'nonexistent', filename: 'file' }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`\n--- 测试 ${index + 1}: ${testCase.prefix}:${testCase.filename} ---`);

        const anchor = plugin.findSpecificLinkInDocument(testCase.prefix, testCase.filename);

        if (anchor) {
            console.log(`✅ 找到锚点: "${anchor}"`);
        } else {
            console.log(`❌ 未找到锚点`);
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 测试光标检测性能
export function testCursorPerformance() {
    console.log('=== 测试光标检测性能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    console.log('请将光标放在一个跨库链接上');

    // 测试光标检测
    console.log('\n🚀 测试光标检测:');
    const start = performance.now();
    const currentLink = plugin.findCurrentHoveredLink();
    const end = performance.now();

    console.log(`光标检测耗时: ${(end - start).toFixed(2)}ms`);
    console.log(`检测到的链接: "${currentLink}"`);

    if (currentLink) {
        console.log('✅ 光标检测成功');
    } else {
        console.log('❌ 未检测到链接，请确保光标在跨库链接上');
    }

    console.log('\n=== 性能测试完成 ===');
}

// 测试缓存问题修复
export function testCacheFix() {
    console.log('=== 测试缓存问题修复 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 清理缓存
    const linkCache = (window as any)._crossVaultLinkCache;
    if (linkCache) {
        linkCache.clear();
        console.log('已清理缓存');
    }

    // 测试不同的链接
    const testLinks = [
        'test:aa',      // 基本文件
        'test:aa#A1',   // 章节
        'test:aa#^1'    // 块引用
    ];

    console.log('\n📄 当前文档中的链接:');
    plugin.scanCurrentDocumentForDebug();

    testLinks.forEach((linkPath, index) => {
        console.log(`\n--- 测试 ${index + 1}: ${linkPath} ---`);

        // 解析链接
        const parsed = plugin.parseCrossVaultLink(linkPath);
        if (parsed) {
            console.log(`解析结果: 前缀="${parsed.prefix}", 文件="${parsed.filename}", 锚点="${parsed.anchor}", 类型="${parsed.anchorType}"`);

            // 测试文件对象创建
            const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${parsed.prefix}:${parsed.filename}`, '');
            if (fileObj) {
                console.log(`文件对象: 路径="${fileObj.path}", 锚点="${fileObj.anchor}"`);

                // 测试内容获取
                plugin.app.vault.cachedRead(fileObj)
                    .then((content: string) => {
                        console.log(`✅ 内容获取成功: ${content.length} 字符`);
                        if (content.length === 0) {
                            console.log(`❌ 但内容为空！`);
                        } else {
                            console.log(`内容预览: "${content.substring(0, 50)}..."`);
                        }
                    })
                    .catch((error: Error) => {
                        console.log(`❌ 内容获取失败: ${error.message}`);
                    });
            } else {
                console.log(`❌ 文件对象创建失败`);
            }
        } else {
            console.log(`❌ 链接解析失败`);
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 清理所有缓存
export function clearAllCaches() {
    console.log('=== 清理所有缓存 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 清理链接缓存
    const linkCache = (window as any)._crossVaultLinkCache;
    if (linkCache) {
        const size = linkCache.size;
        linkCache.clear();
        console.log(`✅ 已清理链接缓存: ${size} 个条目`);
    }

    // 清理远程文件缓存
    if (plugin.remoteCacheMap) {
        const size = plugin.remoteCacheMap.size;
        plugin.remoteCacheMap.clear();
        console.log(`✅ 已清理远程文件缓存: ${size} 个条目`);
    }

    console.log('=== 清理完成 ===');
}

// 测试多 vault 直接读取功能
export function testMultiVaultDirectRead() {
    console.log('=== 测试多 vault 直接读取功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 1. 检查多 vault 支持
    console.log('\n🔍 步骤1: 检查多 vault 支持');
    const allVaults = (plugin.app as any).vaults?.getLoadedVaults?.();
    if (!allVaults) {
        console.log('❌ 当前 Obsidian 版本不支持多 vault 功能');
        return;
    }

    console.log(`✅ 找到 ${allVaults.length} 个已加载的 vault:`);
    allVaults.forEach((vault: any, index: number) => {
        const vaultName = vault.getName?.() || `Vault ${index}`;
        const fileCount = vault.getMarkdownFiles?.()?.length || 0;
        console.log(`  ${index + 1}. ${vaultName} (${fileCount} 个 markdown 文件)`);
    });

    // 2. 获取所有 vault 的元数据
    console.log('\n📊 步骤2: 获取所有 vault 的元数据');
    const vaultsMetadata = plugin.getAllVaultsMetadata();
    console.log(`获取到 ${vaultsMetadata.length} 个 vault 的元数据:`);
    vaultsMetadata.forEach((vaultInfo: any) => {
        console.log(`  - ${vaultInfo.name}: ${vaultInfo.files.length} 个文件`);
        vaultInfo.files.slice(0, 3).forEach((file: any) => {
            console.log(`    * ${file.basename} (${file.metadata?.headings?.length || 0} 个标题)`);
        });
        if (vaultInfo.files.length > 3) {
            console.log(`    ... 还有 ${vaultInfo.files.length - 3} 个文件`);
        }
    });

    // 3. 测试直接读取功能
    console.log('\n📖 步骤3: 测试直接读取功能');

    // 测试用例
    const testCases = [
        { prefix: 'test', filename: 'aa', anchor: undefined, description: '读取整个文件' },
        { prefix: 'test', filename: 'aa', anchor: 'A1', description: '读取特定章节' },
        { prefix: 'test', filename: 'aa', anchor: '^1', description: '读取特定块' }
    ];

    testCases.forEach(async (testCase, index) => {
        setTimeout(async () => {
            console.log(`\n🧪 测试 ${index + 1}: ${testCase.description}`);
            console.log(`   目标: ${testCase.prefix}:${testCase.filename}${testCase.anchor ? '#' + testCase.anchor : ''}`);

            try {
                const content = await plugin.readCrossVaultContentDirect(
                    testCase.prefix,
                    testCase.filename,
                    testCase.anchor
                );

                if (content !== null) {
                    console.log(`   ✅ 读取成功，内容长度: ${content.length}`);
                    console.log(`   📝 内容预览: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`);
                } else {
                    console.log(`   ❌ 读取失败: 未找到内容`);
                }
            } catch (error) {
                console.log(`   ❌ 读取失败:`, error.message);
            }
        }, index * 2000);
    });

    console.log('\n=== 多 vault 测试完成 ===');
}

// 比较直接读取和 API 读取的性能
export function compareReadMethods() {
    console.log('=== 比较直接读取和 API 读取的性能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    const testFile = { prefix: 'test', filename: 'aa' };

    // 测试直接读取
    console.log('\n⚡ 测试直接读取性能');
    const directStartTime = performance.now();

    plugin.readCrossVaultContentDirect(testFile.prefix, testFile.filename)
        .then((content: string | null) => {
            const directEndTime = performance.now();
            const directTime = directEndTime - directStartTime;

            console.log(`✅ 直接读取完成: ${directTime.toFixed(2)}ms`);
            console.log(`📝 内容长度: ${content?.length || 0}`);

            // 测试 API 读取
            console.log('\n🌐 测试 API 读取性能');
            const apiStartTime = performance.now();

            const connection = plugin.settings.connections.find((conn: any) =>
                conn.description === testFile.prefix
            );

            if (connection) {
                const apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${testFile.filename}`;

                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        const apiEndTime = performance.now();
                        const apiTime = apiEndTime - apiStartTime;

                        console.log(`✅ API 读取完成: ${apiTime.toFixed(2)}ms`);
                        console.log(`📝 内容长度: ${data.content?.length || 0}`);

                        // 性能比较
                        console.log(`\n📊 性能比较:`);
                        console.log(`   直接读取: ${directTime.toFixed(2)}ms`);
                        console.log(`   API 读取: ${apiTime.toFixed(2)}ms`);
                        console.log(`   性能提升: ${((apiTime - directTime) / apiTime * 100).toFixed(1)}%`);
                    })
                    .catch(error => {
                        console.log(`❌ API 读取失败:`, error);
                    });
            } else {
                console.log(`❌ 未找到连接配置: ${testFile.prefix}`);
            }
        })
        .catch((error: any) => {
            console.log(`❌ 直接读取失败:`, error);
        });

    console.log('=== 性能比较测试启动 ===');
}

// 测试跨库文件信息 API
export function testCrossVaultFileInfo() {
    console.log('=== 测试跨库文件信息 API ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 测试用例
    const testCases = [
        { prefix: 'test', filename: 'aa', description: '测试基本文件信息' },
        { prefix: 'test', filename: 'nonexistent', description: '测试不存在的文件' },
        { prefix: 'invalid', filename: 'aa', description: '测试无效的前缀' }
    ];

    testCases.forEach(async (testCase, index) => {
        setTimeout(async () => {
            console.log(`\n🧪 测试 ${index + 1}: ${testCase.description}`);
            console.log(`   目标: ${testCase.prefix}:${testCase.filename}`);

            try {
                // 使用插件接口获取文件信息
                const pluginInterface = (plugin.app as any).plugins.plugins['connect-vault-interface'];
                if (pluginInterface && pluginInterface.getCrossVaultFileInfo) {
                    const fileInfo = await pluginInterface.getCrossVaultFileInfo(testCase.prefix, testCase.filename);

                    if (fileInfo) {
                        console.log(`   ✅ 获取信息成功:`);
                        console.log(`      - 文件存在: ${fileInfo.exists}`);
                        console.log(`      - 文件路径: ${fileInfo.path || 'N/A'}`);

                        if (fileInfo.info) {
                            console.log(`      - 文件大小: ${fileInfo.info.size || 0} 字节`);
                            console.log(`      - 标题数量: ${fileInfo.info.metadata?.headings || 0}`);
                            console.log(`      - 块引用数量: ${fileInfo.info.metadata?.blocks || 0}`);
                            console.log(`      - 链接数量: ${fileInfo.info.metadata?.links || 0}`);
                            console.log(`      - 标签数量: ${fileInfo.info.metadata?.tags || 0}`);

                            if (fileInfo.info.content) {
                                console.log(`      - 内容统计:`);
                                console.log(`        * 字符数: ${fileInfo.info.content.characters}`);
                                console.log(`        * 行数: ${fileInfo.info.content.lines}`);
                                console.log(`        * 单词数: ${fileInfo.info.content.words}`);
                            }

                            if (fileInfo.info.headings && fileInfo.info.headings.length > 0) {
                                console.log(`      - 标题列表:`);
                                fileInfo.info.headings.slice(0, 3).forEach((heading: any) => {
                                    console.log(`        * ${'#'.repeat(heading.level)} ${heading.heading}`);
                                });
                                if (fileInfo.info.headings.length > 3) {
                                    console.log(`        ... 还有 ${fileInfo.info.headings.length - 3} 个标题`);
                                }
                            }

                            if (fileInfo.info.blocks && fileInfo.info.blocks.length > 0) {
                                console.log(`      - 块引用: ${fileInfo.info.blocks.join(', ')}`);
                            }
                        }
                    } else {
                        console.log(`   ❌ 获取信息失败: 返回 null`);
                    }
                } else {
                    console.log(`   ❌ 插件接口不可用`);
                }
            } catch (error) {
                console.log(`   ❌ 获取信息失败:`, error.message);
            }
        }, index * 2000);
    });

    console.log('\n=== 跨库文件信息测试启动 ===');
}

// 直接测试 info API
export function testInfoAPI() {
    console.log('=== 直接测试 Info API ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    const testCases = [
        { prefix: 'test', filename: 'aa' },
        { prefix: 'test', filename: 'nonexistent' }
    ];

    testCases.forEach(async (testCase, index) => {
        setTimeout(async () => {
            console.log(`\n🌐 直接 API 测试 ${index + 1}: ${testCase.prefix}:${testCase.filename}`);

            const connection = plugin.settings.connections.find((conn: any) =>
                conn.description === testCase.prefix
            );

            if (!connection) {
                console.log(`   ❌ 未找到连接配置: ${testCase.prefix}`);
                return;
            }

            try {
                const apiUrl = `http://127.0.0.1:${connection.port}/api/note/info?name=${encodeURIComponent(testCase.filename)}`;
                console.log(`   📡 请求 URL: ${apiUrl}`);

                const response = await fetch(apiUrl);
                console.log(`   📊 响应状态: ${response.status} ${response.statusText}`);

                if (response.ok) {
                    const data = await response.json();
                    console.log(`   ✅ API 响应成功:`);
                    console.log(`      响应数据:`, JSON.stringify(data, null, 2));
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ API 响应失败: ${errorText}`);
                }
            } catch (error) {
                console.log(`   ❌ API 请求失败:`, error.message);
            }
        }, index * 1500);
    });

    console.log('\n=== 直接 API 测试启动 ===');
}

// 测试路径信息功能
export function testPathInfo() {
    console.log('=== 测试路径信息功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 测试不同路径结构的文件
    const testCases = [
        { prefix: 'test', filename: 'aa', description: '根目录文件' },
        { prefix: 'test', filename: 'subfolder/file', description: '子文件夹中的文件' },
        { prefix: 'test', filename: 'deep/nested/folder/file', description: '深层嵌套文件' }
    ];

    testCases.forEach(async (testCase, index) => {
        setTimeout(async () => {
            console.log(`\n📁 路径测试 ${index + 1}: ${testCase.description}`);
            console.log(`   目标: ${testCase.prefix}:${testCase.filename}`);

            const connection = plugin.settings.connections.find((conn: any) =>
                conn.description === testCase.prefix
            );

            if (!connection) {
                console.log(`   ❌ 未找到连接配置: ${testCase.prefix}`);
                return;
            }

            try {
                const apiUrl = `http://127.0.0.1:${connection.port}/api/note/info?name=${encodeURIComponent(testCase.filename)}`;
                console.log(`   📡 请求 URL: ${apiUrl}`);

                const response = await fetch(apiUrl);
                console.log(`   📊 响应状态: ${response.status} ${response.statusText}`);

                if (response.ok) {
                    const data = await response.json();
                    console.log(`   ✅ 路径信息获取成功:`);

                    if (data.info) {
                        const info = data.info;
                        console.log(`      📂 路径信息:`);
                        console.log(`         - 库内相对路径: ${info.relativePath || 'N/A'}`);
                        console.log(`         - 系统绝对路径: ${info.absolutePath || 'N/A'}`);
                        console.log(`         - Vault 根路径: ${info.vaultPath || 'N/A'}`);
                        console.log(`         - Vault 名称: ${info.vaultName || 'N/A'}`);
                        console.log(`         - 文件夹路径: ${info.folderPath || '(根目录)'}`);
                        console.log(`         - 父文件夹名: ${info.folderName || '(无)'}`);
                        console.log(`         - 文件夹深度: ${info.depth || 0}`);

                        if (info.pathSegments && info.pathSegments.length > 0) {
                            console.log(`         - 路径分段: [${info.pathSegments.join(' → ')}]`);
                        }

                        console.log(`      📄 文件信息:`);
                        console.log(`         - 文件名: ${info.name || 'N/A'}`);
                        console.log(`         - 基础名: ${info.basename || 'N/A'}`);
                        console.log(`         - 扩展名: ${info.extension || 'N/A'}`);
                        console.log(`         - 文件大小: ${info.size || 0} 字节`);

                        if (info.created) {
                            console.log(`         - 创建时间: ${new Date(info.created).toLocaleString()}`);
                        }
                        if (info.modified) {
                            console.log(`         - 修改时间: ${new Date(info.modified).toLocaleString()}`);
                        }
                    }
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ API 响应失败: ${errorText}`);
                }
            } catch (error) {
                console.log(`   ❌ API 请求失败:`, error.message);
            }
        }, index * 2000);
    });

    console.log('\n=== 路径信息测试启动 ===');
}

// 测试光标位置检测
export function testCursorDetection() {
    console.log('=== 测试光标位置检测 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    console.log('请将光标放在一个跨库链接上，然后运行此测试');

    const currentLink = plugin.findCurrentHoveredLink();
    if (currentLink) {
        console.log(`✅ 检测到当前链接: "${currentLink}"`);

        const parsed = plugin.parseCrossVaultLink(currentLink);
        if (parsed) {
            console.log(`解析结果:`);
            console.log(`  前缀: "${parsed.prefix}"`);
            console.log(`  文件名: "${parsed.filename}"`);
            console.log(`  锚点: "${parsed.anchor}"`);
            console.log(`  类型: ${parsed.anchorType}`);
        } else {
            console.log(`❌ 链接解析失败`);
        }
    } else {
        console.log(`❌ 未检测到跨库链接，请确保光标在链接上`);
    }

    console.log('\n=== 测试完成 ===');
}

// 模拟悬停测试
export function simulateHover(linkText: string) {
    console.log(`=== 模拟悬停测试: ${linkText} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 解析链接
    const parsed = plugin.parseCrossVaultLink(linkText);
    if (!parsed) {
        console.log('❌ 不是有效的跨库链接');
        return;
    }

    console.log(`解析链接: 前缀="${parsed.prefix}", 文件="${parsed.filename}", 锚点="${parsed.anchor}", 类型="${parsed.anchorType}"`);

    // 模拟 getFirstLinkpathDest 调用
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${parsed.prefix}:${parsed.filename}`, '');
    if (fileObj) {
        console.log(`✅ 文件对象创建成功:`);
        console.log(`  路径: ${fileObj.path}`);
        console.log(`  锚点: ${fileObj.anchor}`);

        // 测试内容获取
        plugin.app.vault.cachedRead(fileObj)
            .then((content: string) => {
                console.log(`✅ 内容获取成功: ${content.length} 字符`);
                if (content.length > 0) {
                    console.log(`内容预览: "${content.substring(0, 100)}..."`);
                } else {
                    console.log(`❌ 内容为空`);
                }
            })
            .catch((error: Error) => {
                console.log(`❌ 内容获取失败: ${error.message}`);
            });
    } else {
        console.log(`❌ 文件对象创建失败`);
    }

    console.log('\n=== 模拟测试完成 ===');
}

// 测试精简版本的功能
export function testMinimalVersion() {
    console.log('=== 测试精简版本功能 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    console.log('✅ 插件实例找到');

    // 1. 测试光标检测
    console.log('\n🎯 步骤1: 测试光标检测');
    console.log('请将光标放在一个跨库链接上（如 [[test:aa#^1]]）');

    const currentLink = plugin.findCurrentHoveredLink();
    if (currentLink) {
        console.log(`✅ 检测到链接: ${currentLink}`);

        // 2. 测试链接解析
        console.log('\n🔍 步骤2: 测试链接解析');
        const parsed = plugin.parseCrossVaultLink(currentLink);
        if (parsed) {
            console.log(`✅ 解析成功:`);
            console.log(`  前缀: "${parsed.prefix}"`);
            console.log(`  文件: "${parsed.filename}"`);
            console.log(`  锚点: "${parsed.anchor}"`);
            console.log(`  类型: ${parsed.anchorType}`);

            // 3. 测试文件对象创建
            console.log('\n📁 步骤3: 测试文件对象创建');
            const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(`${parsed.prefix}:${parsed.filename}`, '');
            if (fileObj) {
                console.log(`✅ 文件对象创建成功:`);
                console.log(`  路径: ${fileObj.path}`);
                console.log(`  锚点: ${fileObj.anchor}`);

                // 4. 测试内容获取
                console.log('\n📖 步骤4: 测试内容获取');
                plugin.app.vault.cachedRead(fileObj)
                    .then((content: string) => {
                        if (content && content.length > 0) {
                            console.log(`✅ 内容获取成功: ${content.length} 字符`);
                            console.log(`内容预览: "${content.substring(0, 50)}..."`);
                            console.log('\n🎉 所有功能测试通过！');
                        } else {
                            console.log(`❌ 内容为空`);
                        }
                    })
                    .catch((error: Error) => {
                        console.log(`❌ 内容获取失败: ${error.message}`);
                    });
            } else {
                console.log(`❌ 文件对象创建失败`);
            }
        } else {
            console.log(`❌ 链接解析失败`);
        }
    } else {
        console.log(`❌ 未检测到跨库链接`);
        console.log('请确保光标在跨库链接上，如: [[test:aa#^1]]');
    }

    console.log('\n=== 测试完成 ===');
}

// 测试悬停检测的详细提示
export function testHoverDetection() {
    console.log('=== 测试悬停检测详细提示 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    console.log('📋 使用说明:');
    console.log('1. 将光标放在一个跨库链接上（如 [[test:aa#^1]]）');
    console.log('2. 然后悬停在该链接上');
    console.log('3. 观察控制台输出的详细检测信息');
    console.log('');
    console.log('🎯 当前光标位置检测:');

    const currentLink = plugin.findCurrentHoveredLink();
    if (currentLink) {
        console.log(`✅ 当前检测到的链接: [[${currentLink}]]`);

        const parsed = plugin.parseCrossVaultLink(currentLink);
        if (parsed) {
            console.log(`📋 解析结果:`);
            console.log(`  前缀: "${parsed.prefix}"`);
            console.log(`  文件: "${parsed.filename}"`);
            console.log(`  锚点: "${parsed.anchor}"`);
            console.log(`  类型: ${parsed.anchorType}`);
        }
    } else {
        console.log(`❌ 当前未检测到跨库链接`);
    }

    console.log('');
    console.log('💡 提示: 现在悬停在任何跨库链接上，都会看到详细的检测过程！');
    console.log('=== 测试完成 ===');
}

// 清理控制台并准备测试
export function clearConsoleAndTest() {
    console.clear();
    console.log('🧹 控制台已清理');
    console.log('');
    console.log('🎯 现在请悬停在一个跨库链接上，观察详细的检测过程！');
    console.log('例如: [[test:aa#^1]] 或 [[test:aa#A1]]');
    console.log('');
}

// 测试新的文档搜索方法
export function testDocumentSearch() {
    console.log('=== 测试文档搜索方法 ===');

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    // 测试不同的搜索关键词
    const testCases = [
        'test:aa',
        'test:bb',
        'market:file',
        'nonexistent:file'
    ];

    testCases.forEach((searchTerm, index) => {
        console.log(`\n--- 测试 ${index + 1}: 搜索 "${searchTerm}" ---`);

        const fullLink = plugin.findFullLinkInDocument(searchTerm);
        if (fullLink) {
            console.log(`✅ 找到完整链接: [[${fullLink}]]`);

            const parsed = plugin.parseCrossVaultLink(fullLink);
            if (parsed) {
                console.log(`📋 解析结果:`);
                console.log(`  前缀: "${parsed.prefix}"`);
                console.log(`  文件: "${parsed.filename}"`);
                console.log(`  锚点: "${parsed.anchor}"`);
                console.log(`  类型: ${parsed.anchorType}`);
            }
        } else {
            console.log(`❌ 未找到匹配的链接`);
        }
    });

    console.log('\n=== 测试完成 ===');
}

// 模拟 Obsidian 的悬停调用
export function simulateObsidianHover(linkpath: string) {
    console.log(`=== 模拟 Obsidian 悬停: ${linkpath} ===`);

    const plugin = (window as any).app?.plugins?.plugins?.['connect-vault'];
    if (!plugin) {
        console.log('❌ 未找到插件实例');
        return;
    }

    console.log(`🔍 模拟 getFirstLinkpathDest 调用: linkpath="${linkpath}"`);

    // 直接调用我们的方法
    const fileObj = plugin.app.metadataCache.getFirstLinkpathDest(linkpath, '模板/1.md');

    if (fileObj) {
        console.log(`✅ 文件对象创建成功:`);
        console.log(`  路径: ${fileObj.path}`);
        console.log(`  锚点: ${fileObj.anchor}`);

        // 测试内容获取
        plugin.app.vault.cachedRead(fileObj)
            .then((content: string) => {
                if (content && content.length > 0) {
                    console.log(`✅ 内容获取成功: ${content.length} 字符`);
                    console.log(`内容预览: "${content.substring(0, 50)}..."`);
                } else {
                    console.log(`❌ 内容为空`);
                }
            })
            .catch((error: Error) => {
                console.log(`❌ 内容获取失败: ${error.message}`);
            });
    } else {
        console.log(`❌ 文件对象创建失败`);
    }

    console.log('\n=== 模拟测试完成 ===');
}

// 将调试函数暴露到全局，方便在控制台调用
(window as any).debugCrossVaultLinks = debugCrossVaultLinks;
(window as any).checkAllLinks = checkAllLinks;
(window as any).manualTriggerHiding = manualTriggerHiding;
(window as any).testNewImplementation = testNewImplementation;
(window as any).forceRefreshSmartLinks = forceRefreshSmartLinks;
(window as any).testCodeMirrorDecorations = testCodeMirrorDecorations;
(window as any).manualTriggerPostProcessor = manualTriggerPostProcessor;
(window as any).checkPluginStatus = checkPluginStatus;
(window as any).testApiEndpoint = testApiEndpoint;
(window as any).testObsidianPreview = testObsidianPreview;
(window as any).testHeadingLinkParsing = testHeadingLinkParsing;
(window as any).testActualPreview = testActualPreview;
(window as any).testRemoteFileCache = testRemoteFileCache;
(window as any).clearRemoteCache = clearRemoteCache;
(window as any).testCrossVaultNavigation = testCrossVaultNavigation;
(window as any).testSingleNavigation = testSingleNavigation;
(window as any).testUriFormats = testUriFormats;
(window as any).testLinkTransformation = testLinkTransformation;
(window as any).testRemoteContentTransformation = testRemoteContentTransformation;
(window as any).testEditApi = testEditApi;
(window as any).manualEditTest = manualEditTest;
(window as any).testCrossVaultEdit = testCrossVaultEdit;
(window as any).testSingleCrossVaultEdit = testSingleCrossVaultEdit;
(window as any).testOpenCrossVaultFile = testOpenCrossVaultFile;
(window as any).debugBlockReference = debugBlockReference;
(window as any).testBlockIdFormats = testBlockIdFormats;
(window as any).testBlockReferencePreview = testBlockReferencePreview;
(window as any).testAnchorResolution = testAnchorResolution;
(window as any).scanCurrentDocumentLinks = scanCurrentDocumentLinks;
(window as any).debugBlockReferenceComplete = debugBlockReferenceComplete;
(window as any).forceRefreshLinkCache = forceRefreshLinkCache;
(window as any).testSpecificBlockId = testSpecificBlockId;
(window as any).testNewLinkParsing = testNewLinkParsing;
(window as any).testPreciseLinkFinding = testPreciseLinkFinding;
(window as any).testCursorPerformance = testCursorPerformance;
(window as any).testCacheFix = testCacheFix;
(window as any).clearAllCaches = clearAllCaches;
(window as any).testCursorDetection = testCursorDetection;
(window as any).simulateHover = simulateHover;
(window as any).testMinimalVersion = testMinimalVersion;
(window as any).testHoverDetection = testHoverDetection;
(window as any).clearConsoleAndTest = clearConsoleAndTest;
(window as any).testDocumentSearch = testDocumentSearch;
(window as any).simulateObsidianHover = simulateObsidianHover;