# Connect Vault Plugin - 配置示例

## 场景一：个人知识管理系统

### 库结构
- **主库**（Daily Notes）- 端口 3000
- **项目库**（Projects）- 端口 3001  
- **学习库**（Learning）- 端口 3002
- **归档库**（Archive）- 端口 3003

### 配置步骤

1. **主库配置**：
   ```
   API Server Port: 3000
   
   连接配置：
   - 端口: 3001, 前缀: "proj", 库名: "项目库"
   - 端口: 3002, 前缀: "learn", 库名: "学习库"  
   - 端口: 3003, 前缀: "archive", 库名: "归档库"
   ```

2. **使用示例**：
   ```markdown
   # 今日计划 2024-01-15
   
   ## 项目相关
   - 查看 [[proj:网站重构方案]]
   - 更新 [[proj:API文档]]
   
   ## 学习计划
   - 复习 [[learn:TypeScript高级特性]]
   - 阅读 [[learn:设计模式笔记]]
   
   ## 参考资料
   - [[archive:去年的项目总结]]
   ```

## 场景二：团队协作

### 库结构
- **个人库**（Personal）- 端口 3000
- **团队共享库**（Team Shared）- 端口 3001
- **客户资料库**（Clients）- 端口 3002

### 配置步骤

1. **个人库配置**：
   ```
   API Server Port: 3000
   
   连接配置：
   - 端口: 3001, 前缀: "team", 库名: "团队共享"
   - 端口: 3002, 前缀: "client", 库名: "客户资料"
   ```

2. **使用示例**：
   ```markdown
   # 客户会议准备
   
   ## 会议资料
   - [[team:产品介绍模板]]
   - [[client:ABC公司需求分析]]
   - [[team:报价模板]]
   
   ## 个人笔记
   - 准备要点：参考 [[team:销售话术指南]]
   - 技术问题：查看 [[team:常见技术FAQ]]
   ```

## 场景三：学术研究

### 库结构
- **当前研究**（Current Research）- 端口 3000
- **文献库**（Literature）- 端口 3001
- **方法库**（Methods）- 端口 3002
- **数据库**（Data）- 端口 3003

### 配置步骤

1. **当前研究库配置**：
   ```
   API Server Port: 3000
   
   连接配置：
   - 端口: 3001, 前缀: "lit", 库名: "文献库"
   - 端口: 3002, 前缀: "method", 库名: "方法库"
   - 端口: 3003, 前缀: "data", 库名: "数据库"
   ```

2. **使用示例**：
   ```markdown
   # 实验设计方案
   
   ## 理论基础
   - [[lit:Smith2023_机器学习综述]]
   - [[lit:Jones2022_深度学习应用]]
   
   ## 方法参考
   - [[method:数据预处理流程]]
   - [[method:模型评估指标]]
   
   ## 数据来源
   - [[data:训练数据集说明]]
   - [[data:测试数据集说明]]
   ```

## 场景四：多语言内容管理

### 库结构
- **中文库**（Chinese）- 端口 3000
- **英文库**（English）- 端口 3001
- **日文库**（Japanese）- 端口 3002

### 配置步骤

1. **中文库配置**：
   ```
   API Server Port: 3000
   
   连接配置：
   - 端口: 3001, 前缀: "en", 库名: "English"
   - 端口: 3002, 前缀: "jp", 库名: "Japanese"
   ```

2. **使用示例**：
   ```markdown
   # 产品文档翻译项目
   
   ## 原文参考
   - [[en:Product_Overview]]
   - [[en:User_Manual]]
   
   ## 日文版本
   - [[jp:製品概要]]
   - [[jp:ユーザーマニュアル]]
   
   ## 翻译注意事项
   - 技术术语对照：[[en:Technical_Glossary]]
   ```

## 高级配置技巧

### 1. 端口规划建议

```
基础端口：3000-3009（个人库）
项目端口：3010-3019（项目相关库）
团队端口：3020-3029（团队共享库）
归档端口：3030-3039（归档库）
```

### 2. 前缀命名规范

```
个人相关：personal, me, private
工作相关：work, proj, team
学习相关：learn, study, course
归档相关：archive, old, backup
特殊用途：temp, draft, template
```

### 3. 库名命名建议

使用清晰、简洁的中文或英文名称：
- ✅ 好的命名：项目库、学习笔记、工作文档
- ❌ 避免：库1、temp、新建文件夹

### 4. 网络配置注意事项

1. **端口冲突检查**：
   ```bash
   netstat -an | findstr :3000
   ```

2. **防火墙设置**：
   确保本地回环地址（127.0.0.1）不被阻止

3. **性能优化**：
   - 避免同时连接过多库（建议不超过10个）
   - 大文件库可能影响预览速度

## 故障排除配置

### 常见配置错误

1. **端口重复**：
   ```
   错误：两个库使用相同端口
   解决：为每个库分配唯一端口
   ```

2. **前缀冲突**：
   ```
   错误：多个连接使用相同前缀
   解决：确保每个连接的前缀唯一
   ```

3. **网络问题**：
   ```
   错误：无法连接到远程库
   解决：检查目标库是否启动，端口是否正确
   ```

### 调试配置

在浏览器控制台查看详细日志：
```javascript
// 查看当前连接状态
console.log('Plugin connections:', app.plugins.plugins['connect-vault'].settings.connections);

// 测试特定连接
fetch('http://127.0.0.1:3001/api/notes')
  .then(r => r.json())
  .then(console.log);
```

## 最佳实践

1. **渐进式配置**：先配置一个连接，测试成功后再添加更多
2. **文档化配置**：记录每个库的用途和端口分配
3. **定期备份**：备份插件配置和所有库数据
4. **性能监控**：注意大量跨库链接对性能的影响
