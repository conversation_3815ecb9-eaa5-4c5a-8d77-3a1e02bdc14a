# Connect Vault Plugin 使用指南

一个用于连接多个 Obsidian 库的插件，允许您在当前库中引用和预览其他库的笔记内容。

## 功能特性

- 🔗 **跨库链接**：使用特殊语法链接到其他 Obsidian 库的笔记
- 👁️ **悬停预览**：按住 Ctrl 键悬停在链接上即可预览远程笔记内容
- 🔍 **智能建议**：输入链接时自动提示远程库中的文件名
- 🌐 **API 服务器**：为每个库启动本地 API 服务器
- ⚙️ **多库管理**：支持同时连接多个不同的 Obsidian 库

## 安装方法

### 手动安装

1. 下载插件文件到您的 `.obsidian/plugins/connect-vault/` 目录
2. 确保目录中包含以下文件：
   - `main.js`
   - `manifest.json`
   - `styles.css`
3. 在 Obsidian 设置中启用插件

### 开发安装

1. 克隆此仓库到您的插件目录：
   ```bash
   git clone <repository-url> .obsidian/plugins/connect-vault
   ```
2. 安装依赖：
   ```bash
   cd .obsidian/plugins/connect-vault
   npm install
   ```
3. 构建插件：
   ```bash
   npm run build
   ```
4. 在 Obsidian 中启用插件

## 配置设置

### 1. 基本设置

在 Obsidian 设置 → 社区插件 → Connect Vault 中进行配置：

- **API Server Port**：设置当前库的 API 服务器端口（默认：3000）

### 2. 添加远程库连接

1. 点击"添加连接"按钮
2. 为每个连接配置：
   - **端口号**：远程库的 API 服务器端口
   - **前置内容**：用于识别该库的前缀标识符
   - **库名**：远程库的名称（可选）

### 3. 测试连接

点击"测试连接"按钮验证 API 服务器是否正常运行。

## 使用方法

### 链接语法

使用以下语法创建跨库链接：

```markdown
[[前缀:文件名]]
```

例如：
- `[[work:项目计划]]` - 链接到 "work" 库中的"项目计划"笔记
- `[[personal:日记模板]]` - 链接到 "personal" 库中的"日记模板"笔记

### 悬停预览

1. 按住 `Ctrl` 键（Mac 上是 `Cmd` 键）
2. 将鼠标悬停在跨库链接上
3. 会显示一个预览窗口，显示远程笔记的内容
4. 预览窗口中的链接也支持进一步的悬停预览

### 智能建议

1. 输入 `[[前缀:` 时会自动触发文件名建议
2. 系统会从对应的远程库获取文件列表
3. 选择建议的文件名会自动补全链接

## 设置示例

假设您有三个 Obsidian 库：

1. **主库**（当前库）- 端口 3000
2. **工作库** - 端口 3001  
3. **个人库** - 端口 3002

### 配置步骤：

1. **在主库中安装插件**
2. **配置连接**：
   - 连接1：端口 3001，前缀 "work"，库名 "工作库"
   - 连接2：端口 3002，前缀 "personal"，库名 "个人库"

3. **在其他库中也安装插件**：
   - 工作库：设置端口为 3001
   - 个人库：设置端口为 3002

### 使用示例：

在主库中创建链接：
```markdown
# 今日任务
- 查看 [[work:周报模板]]
- 更新 [[personal:读书笔记]]
- 参考 [[work:项目文档]]
```

## 工作原理

### API 服务器

每个启用了插件的 Obsidian 库都会启动一个本地 HTTP API 服务器，提供以下端点：

- `GET /api/notes` - 获取库中所有笔记列表
- `GET /api/note?name=文件名` - 获取指定笔记内容
- `GET /api/resource?name=文件名` - 获取资源文件（图片等）

### 链接解析

插件会拦截 Obsidian 的内部链接处理机制：

1. **链接识别**：检测 `前缀:文件名` 格式的链接
2. **远程获取**：通过 API 从对应库获取内容
3. **本地渲染**：在当前库中渲染远程内容

### 悬停预览

使用 Obsidian 的 HoverPopover API 实现：

1. **事件监听**：监听 Ctrl+鼠标悬停事件
2. **内容获取**：异步获取远程笔记内容
3. **Markdown 渲染**：使用 MarkdownRenderer 渲染内容
4. **嵌套支持**：预览窗口中的链接也支持进一步预览

## 故障排除

### 常见问题

1. **连接失败**
   - 检查端口是否被占用
   - 确认目标库已启用插件
   - 验证防火墙设置

2. **预览不显示**
   - 确认按住了 Ctrl 键
   - 检查链接格式是否正确
   - 查看控制台错误信息

3. **文件建议不工作**
   - 确认前缀配置正确
   - 检查远程库 API 是否响应
   - 验证网络连接

### 调试信息

插件会在浏览器控制台输出详细的调试信息：

1. 打开开发者工具（F12）
2. 查看 Console 标签
3. 搜索插件相关的日志信息

## 注意事项

1. **安全性**：API 服务器仅监听本地地址（127.0.0.1），不会暴露到网络
2. **性能**：大量跨库链接可能影响性能，建议适度使用
3. **同步**：插件不处理库之间的同步，需要其他工具处理
4. **备份**：建议定期备份所有相关库的数据

## 技术细节

### 依赖项

- Obsidian API
- Node.js HTTP 模块
- TypeScript

### 文件结构

```
connect-vault/
├── main.ts              # 主插件文件
├── src/
│   ├── handlers.ts      # 事件处理器
│   ├── server.ts        # API 服务器
│   ├── settings.ts      # 设置界面
│   ├── ui.ts           # UI 组件
│   ├── utils.ts        # 工具函数
│   └── monkey-patches.ts # API 拦截
├── manifest.json        # 插件清单
├── styles.css          # 样式文件
└── package.json        # 项目配置
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持跨库链接和预览
- 智能文件名建议
- 多库连接管理

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。

## 许可证

MIT License
