import { Plugin, TFile, MarkdownView } from 'obsidian';
import { MyPluginSettings } from './settings';
import { VirtualFile, RemoteCacheEntry } from './types';
import { parseCrossVaultLink, buildApiUrl, generateObsidianUri } from './linkParser';
import { transformInternalLinks } from './contentTransformer';

export class MonkeyPatchManager {
    private plugin: Plugin;
    private settings: MyPluginSettings;
    private remoteCacheMap: Map<string, RemoteCacheEntry>;
    private originalMethods: Map<string, any> = new Map();
    private eventHandlerManager: any; // 将在 setEventHandlerManager 中设置

    constructor(plugin: Plugin, settings: MyPluginSettings, remoteCacheMap: Map<string, RemoteCacheEntry>) {
        this.plugin = plugin;
        this.settings = settings;
        this.remoteCacheMap = remoteCacheMap;
    }

    /**
     * 设置事件处理管理器的引用
     */
    setEventHandlerManager(eventHandlerManager: any): void {
        this.eventHandlerManager = eventHandlerManager;
    }

    /**
     * 注册所有的 Monkey Patches
     */
    registerAll(): void {
        // console.log('[Connect Vault] 正在注册猴子补丁...');

        this.registerParseLinkTextPatches();
        this.registerGetFileCachePatch();
        this.registerGetFirstLinkpathDestPatch();
        this.registerCachedReadPatch();
        this.registerExistsPatch();
        this.registerGetResourcePathPatch();
        this.registerModifyPatch();
        this.registerOpenLinkTextPatch();

        // console.log('[Connect Vault] 猴子补丁注册完成');
    }

    /**
     * 恢复所有被修改的方法
     */
    restoreAll(): void {
        console.log('[Connect Vault] 正在恢复原始方法...');
        
        for (const [key, originalMethod] of this.originalMethods) {
            try {
                const [objectPath, methodName] = key.split('.');
                const targetObject = this.getObjectByPath(objectPath);
                if (targetObject && originalMethod) {
                    targetObject[methodName] = originalMethod;
                    console.log(`[Connect Vault] 已恢复 ${key}`);
                }
            } catch (error) {
                console.error(`[Connect Vault] 恢复 ${key} 时出错:`, error);
            }
        }
        
        this.originalMethods.clear();
        console.log('[Connect Vault] 原始方法恢复完成');
    }

    /**
     * 拦截 parseLinktext 函数
     */
    private registerParseLinkTextPatches(): void {
        console.log('[Connect Vault] 检查 parseLinktext 函数...');
        
        const locations = [
            { name: 'window.parseLinktext', path: 'window', method: 'parseLinktext' },
            { name: 'app.parseLinktext', path: 'app', method: 'parseLinktext' },
            { name: 'metadataCache.parseLinktext', path: 'app.metadataCache', method: 'parseLinktext' },
            { name: 'vault.parseLinktext', path: 'app.vault', method: 'parseLinktext' }
        ];
        
        locations.forEach(location => {
            const targetObject = this.getObjectByPath(location.path);
            const originalFunc = targetObject?.[location.method];
            console.log(`[Connect Vault] ${location.name} 函数存在:`, !!originalFunc);
            
            if (originalFunc) {
                this.originalMethods.set(`${location.path}.${location.method}`, originalFunc);
                targetObject[location.method] = this.createParseLinkTextInterceptor(originalFunc);
                console.log(`[Connect Vault] 已拦截 ${location.name}`);
            }
        });

        if (this.originalMethods.size === 0) {
            console.log('[Connect Vault] 未找到 parseLinktext 函数，将在 getFirstLinkpathDest 中处理锚点');
        }
    }

    /**
     * 创建 parseLinktext 拦截器
     */
    private createParseLinkTextInterceptor(originalFunc: any) {
        return (linktext: string) => {
            console.log(`[Connect Vault] 拦截到 parseLinktext 调用: "${linktext}"`);

            const parsed = parseCrossVaultLink(linktext);
            if (parsed) {
                console.log(`[Connect Vault] 跨库链接解析: 前缀="${parsed.prefix}", 文件="${parsed.filename}", 子路径="${parsed.anchor}"`);
                
                return {
                    path: `${parsed.prefix}:${parsed.filename}`,
                    subpath: parsed.anchor
                };
            }

            // 对于普通链接，使用原始方法
            return originalFunc(linktext);
        };
    }

    /**
     * 拦截 getFileCache 方法，合并跨库文件的元数据
     */
    private registerGetFileCachePatch(): void {
        const originalGetFileCache = this.plugin.app.metadataCache.getFileCache.bind(this.plugin.app.metadataCache);
        this.originalMethods.set('app.metadataCache.getFileCache', originalGetFileCache);

        (this.plugin.app.metadataCache as any).getFileCache = (file: any) => {
            // 获取原生缓存
            const originalCache = originalGetFileCache(file);

            if (!file) return originalCache;

            // 安全地获取文件路径
            let filePath: string;
            try {
                filePath = typeof file === 'string' ? file : (file.path || '');
                if (!filePath) return originalCache;
            } catch (error) {
                console.error('[Connect Vault] 获取文件路径时出错:', error);
                return originalCache;
            }

            // 检查是否是跨库文件
            if (filePath && filePath.includes(':')) {
                // console.log(`[Connect Vault] getFileCache 拦截跨库文件: ${filePath}`);

                // 获取跨库文件的自定义元数据
                const customMetadata = (this.plugin as any).crossVaultMetadataCache?.get(filePath);

                if (customMetadata && customMetadata.metadata) {
                    // console.log(`[Connect Vault] 合并跨库文件元数据: ${filePath}`);

                    // 合并原生缓存和自定义缓存
                    const mergedCache = {
                        ...originalCache,
                        ...customMetadata.metadata,
                        // 保留原生缓存的基本信息
                        stat: customMetadata.metadata.stat || originalCache?.stat,
                        hash: customMetadata.metadata.hash || originalCache?.hash,
                        // 添加跨库标识
                        isCrossVault: true,
                        crossVaultPrefix: customMetadata.prefix,
                        crossVaultAnchor: customMetadata.anchor
                    };

                    // console.log(`[Connect Vault] 元数据合并完成:`, {
                    //     headings: mergedCache.headings?.length || 0,
                    //     blocks: Object.keys(mergedCache.blocks || {}).length,
                    //     links: mergedCache.links?.length || 0,
                    //     embeds: mergedCache.embeds?.length || 0
                    // });

                    return mergedCache;
                }

                // 如果没有自定义元数据，返回空的跨库缓存结构
                // console.log(`[Connect Vault] 创建空的跨库缓存结构: ${filePath}`);
                return {
                    links: [],
                    embeds: [],
                    tags: [],
                    headings: [],
                    blocks: {},
                    frontmatter: {},
                    sections: [],
                    listItems: [],
                    hash: `cross-vault-empty-${Date.now()}`,
                    stat: {
                        ctime: Date.now(),
                        mtime: Date.now(),
                        size: 0
                    },
                    isCrossVault: true,
                    crossVaultPrefix: filePath.split(':')[0],
                    crossVaultAnchor: filePath.includes('#') ? filePath.split('#')[1] : undefined
                };
            }

            // 对于普通文件，返回原生缓存
            return originalCache;
        };
    }

    /**
     * 拦截 getFirstLinkpathDest 方法
     */
    private registerGetFirstLinkpathDestPatch(): void {
        const originalGetFileByPath = (this.plugin.app.metadataCache as any).getFirstLinkpathDest;
        this.originalMethods.set('app.metadataCache.getFirstLinkpathDest', originalGetFileByPath);

        console.log(`🔧 [Connect Vault] MonkeyPatch: 原始方法类型:`, typeof originalGetFileByPath);
        console.log(`🔧 [Connect Vault] MonkeyPatch: 原始方法存在:`, !!originalGetFileByPath);
        console.log(`🔧 [Connect Vault] MonkeyPatch: metadataCache 对象:`, !!this.plugin.app.metadataCache);

        this.plugin.registerEvent(this.plugin.app.workspace.on('quit', () => {
            (this.plugin.app.metadataCache as any).getFirstLinkpathDest = originalGetFileByPath;
        }));
        
        (this.plugin.app.metadataCache as any).getFirstLinkpathDest = (linkpath: string, sourcePath: string, subpath?: string) => {
            try {
                // 参数验证
                if (!linkpath || typeof linkpath !== 'string') {
                    return originalGetFileByPath.call(this.plugin.app.metadataCache, linkpath, sourcePath, subpath);
                }

                // 先检查是否是跨库链接格式
                if (!linkpath.includes(':')) {
                    // 对于普通链接，静默处理，不输出日志
                    return originalGetFileByPath.call(this.plugin.app.metadataCache, linkpath, sourcePath, subpath);
                }
            } catch (error) {
                console.error('[Connect Vault] getFirstLinkpathDest 参数处理错误:', error);
                return originalGetFileByPath.call(this.plugin.app.metadataCache, linkpath, sourcePath, subpath);
            }

            // console.log(`✅ [Connect Vault] 确认是跨库链接格式`);

            // 尝试从当前悬停的 DOM 元素直接获取完整链接
            const fullLinkText = this.findFullLinkFromCurrentHover(linkpath);
            // console.log(`🎯 [Connect Vault] 获取到的完整链接: "${fullLinkText}"`);
            const linkTextToProcess = fullLinkText || linkpath;

            // if (fullLinkText && fullLinkText !== linkpath) {
            //     console.log(`🎯 [Connect Vault] 从文档中找到完整链接: "${fullLinkText}"`);
            // } else {
            //     console.log(`🔧 [Connect Vault] 使用传入的链接: "${linkpath}"`);
            // }

            // 检查链接路径是否符合我们的格式
            const parsed = parseCrossVaultLink(linkTextToProcess);
            if (parsed) {
                const { prefix, filename, anchor } = parsed;

                // 如果还是没有锚点，但原始 linkpath 不包含锚点，说明可能是 parseLinktext 没有正确工作
                if (!anchor && linkpath !== linkTextToProcess) {
                    // console.log(`⚠️ [Connect Vault] 从完整链接中重新解析锚点`);
                    const fullMatch = linkTextToProcess.match(/^(.*?):(.*?)#(.*)$/);
                    // if (fullMatch) {
                    //     console.log(`✅ [Connect Vault] 重新解析得到锚点: "${fullMatch[3]}"`);
                    // }
                }

                // if (!anchor) {
                //     console.log(`❌ [Connect Vault] 最终无法确定锚点，将创建无锚点的文件对象`);
                // }

                // 查找对应的连接配置
                const connection = this.settings.connections.find(conn => conn.description === prefix);
                if (connection) {
                    try {
                        // 创建虚拟文件对象
                        const fullPath = `${prefix}:${filename}`;

                        const virtualFile: VirtualFile = {
                            path: fullPath,
                            basename: filename,
                            extension: 'md',
                            name: `${filename}.md`,
                            stat: {
                                ctime: Date.now(),
                                mtime: Date.now(),
                                size: 0
                            },
                            vault: this.plugin.app.vault,
                            parent: null,
                            deleted: false,
                            unsafeCachedData: undefined,
                            // 跨库链接特有属性
                            isCrossVault: true,
                            crossVaultPrefix: prefix,
                            crossVaultAnchor: anchor,
                            crossVaultConnection: connection
                        };

                        // 在后台异步获取文件信息并更新 stat
                        this.fetchFileInfo(connection, filename).then(fileInfo => {
                            if (fileInfo) {
                                virtualFile.stat.ctime = fileInfo.created || virtualFile.stat.ctime;
                                virtualFile.stat.mtime = fileInfo.modified || virtualFile.stat.mtime;
                                virtualFile.stat.size = fileInfo.size || virtualFile.stat.size;
                            }
                        }).catch(error => {
                            // 静默处理错误，不影响虚拟文件的创建
                        });

                        // console.log(`[Connect Vault] 创建虚拟文件对象: ${fullPath}, 锚点: ${anchor}, 支持编辑: true`);
                        return virtualFile;
                    } catch (error) {
                        console.error('[Connect Vault] 创建虚拟文件对象时出错:', error);
                        // 如果创建虚拟文件对象失败，回退到原始方法
                        return originalGetFileByPath.call(this.plugin.app.metadataCache, linkpath, sourcePath, subpath);
                    }
                }
            }

            // 对于普通链接，使用原始方法
            try {
                return originalGetFileByPath.call(this.plugin.app.metadataCache, linkpath, sourcePath, subpath);
            } catch (error) {
                console.error('[Connect Vault] 调用原始 getFirstLinkpathDest 时出错:', error);
                return null;
            }
        };
    }

    /**
     * 获取对象路径对应的对象
     */
    private getObjectByPath(path: string): any {
        const parts = path.split('.');
        let obj: any = window;
        
        for (const part of parts) {
            if (part === 'app') {
                obj = this.plugin.app;
            } else {
                obj = obj?.[part];
            }
            if (!obj) break;
        }
        
        return obj;
    }

    /**
     * 从远程 API 获取文件信息
     */
    private async fetchFileInfo(connection: any, filename: string): Promise<any> {
        try {
            const infoUrl = `http://127.0.0.1:${connection.port}/api/note/info?name=${encodeURIComponent(filename)}`;
            // console.log(`[Connect Vault] 获取文件信息: ${infoUrl}`);

            const response = await fetch(infoUrl);
            if (!response.ok) {
                // console.log(`[Connect Vault] Info API 请求失败: ${response.status}`);
                return null;
            }

            const data = await response.json();
            if (data.exists && data.info) {
                // console.log(`[Connect Vault] 成功获取文件信息: ${filename}`);
                return data.info;
            }

            return null;
        } catch (error) {
            // console.error(`[Connect Vault] 获取文件信息失败:`, error);
            return null;
        }
    }

    /**
     * 创建并缓存跨库文件的元数据
     */
    private async createAndCacheMetadata(virtualFile: VirtualFile, content: string): Promise<void> {
        try {
            // console.log(`[Connect Vault] 创建元数据缓存: ${virtualFile.path}`);

            // 解析内容，提取元数据信息
            const metadata = this.parseContentToFileCache(content, virtualFile);

            // 尝试从 info API 获取额外的文件信息
            let fileInfo = null;
            if (virtualFile.crossVaultConnection) {
                fileInfo = await this.fetchFileInfo(virtualFile.crossVaultConnection, virtualFile.basename);
            }

            // 将元数据存储到插件的自定义缓存中
            const crossVaultMetadata = {
                path: virtualFile.path,
                basename: virtualFile.basename,
                prefix: virtualFile.crossVaultPrefix,
                anchor: virtualFile.crossVaultAnchor,
                metadata: metadata,
                fileInfo: fileInfo, // 添加从 info API 获取的信息
                timestamp: Date.now(),
                content: content
            };

            // 存储到插件的元数据缓存中
            if (!(this.plugin as any).crossVaultMetadataCache) {
                (this.plugin as any).crossVaultMetadataCache = new Map();
            }
            (this.plugin as any).crossVaultMetadataCache.set(virtualFile.path, crossVaultMetadata);

            // 触发自定义事件，通知其他组件元数据已更新
            this.plugin.app.workspace.trigger('cross-vault-metadata-change', virtualFile.path, crossVaultMetadata);

            // console.log(`[Connect Vault] 元数据缓存已创建:`, {
            //     headings: metadata.headings?.length || 0,
            //     blocks: Object.keys(metadata.blocks || {}).length,
            //     links: metadata.links?.length || 0,
            //     embeds: metadata.embeds?.length || 0,
            //     tags: metadata.tags?.length || 0
            // });

        } catch (error) {
            console.error(`[Connect Vault] 创建元数据缓存失败:`, error);
        }
    }

    /**
     * 解析内容并创建 FileCache 对象
     */
    private parseContentToFileCache(content: string, virtualFile: VirtualFile): any {
        const lines = content.split('\n');
        const fileCache: any = {
            links: [],
            embeds: [],
            tags: [],
            headings: [],
            blocks: {},
            frontmatter: {},
            sections: [],
            listItems: [],
            hash: `cross-vault-${Date.now()}`,
            stat: virtualFile.stat
        };

        let currentSection: any = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineNumber = i;

            // 解析标题
            const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
            if (headingMatch) {
                const level = headingMatch[1].length;
                const heading = headingMatch[2].trim();

                const headingObj = {
                    heading,
                    level,
                    position: {
                        start: { line: lineNumber, col: 0, offset: 0 },
                        end: { line: lineNumber, col: line.length, offset: 0 }
                    }
                };

                fileCache.headings.push(headingObj);

                // 创建新的 section
                currentSection = {
                    type: 'heading',
                    position: {
                        start: { line: lineNumber, col: 0, offset: 0 },
                        end: { line: lineNumber, col: line.length, offset: 0 }
                    }
                };
                fileCache.sections.push(currentSection);
            }

            // 解析块引用
            const blockMatch = line.match(/\^([a-zA-Z0-9-_]+)\s*$/);
            if (blockMatch) {
                const blockId = blockMatch[1];
                fileCache.blocks[blockId] = {
                    position: {
                        start: { line: lineNumber, col: 0, offset: 0 },
                        end: { line: lineNumber, col: line.length, offset: 0 }
                    }
                };
            }

            // 解析内部链接
            const linkMatches = line.matchAll(/\[\[([^\]]+)\]\]/g);
            for (const match of linkMatches) {
                const linkText = match[1];
                const isEmbed = line.includes(`![[${linkText}]]`);

                const linkObj = {
                    link: linkText,
                    original: match[0],
                    displayText: linkText,
                    position: {
                        start: { line: lineNumber, col: match.index || 0, offset: 0 },
                        end: { line: lineNumber, col: (match.index || 0) + match[0].length, offset: 0 }
                    }
                };

                if (isEmbed) {
                    fileCache.embeds.push(linkObj);
                } else {
                    fileCache.links.push(linkObj);
                }
            }

            // 解析标签
            const tagMatches = line.matchAll(/#([a-zA-Z0-9/_-]+)/g);
            for (const match of tagMatches) {
                const tag = match[1];
                fileCache.tags.push({
                    tag: `#${tag}`,
                    position: {
                        start: { line: lineNumber, col: match.index || 0, offset: 0 },
                        end: { line: lineNumber, col: (match.index || 0) + match[0].length, offset: 0 }
                    }
                });
            }
        }

        return fileCache;
    }

    /**
     * 拦截 cachedRead 方法
     */
    private registerCachedReadPatch(): void {
        const originalCachedRead = this.plugin.app.vault.cachedRead.bind(this.plugin.app.vault);
        this.originalMethods.set('app.vault.cachedRead', originalCachedRead);

        (this.plugin.app.vault as any).cachedRead = async (file: TFile | VirtualFile): Promise<string> => {
            const virtualFile = file as VirtualFile;

            if (virtualFile.isCrossVault) {
                // console.log(`[Connect Vault] cachedRead 跨库文件: ${virtualFile.path}`);

                const connection = virtualFile.crossVaultConnection;
                const filename = virtualFile.basename;
                const anchor = virtualFile.crossVaultAnchor;

                try {
                    // 首先尝试直接从其他 vault 读取
                    const directContent = await (this.plugin as any).readCrossVaultContentDirect(
                        virtualFile.crossVaultPrefix!,
                        filename,
                        anchor
                    );

                    if (directContent !== null) {
                        // console.log(`[Connect Vault] 直接从 vault 读取成功: ${virtualFile.path}`);

                        // 转换内容中的内部链接
                        const transformedContent = transformInternalLinks(directContent, virtualFile.crossVaultPrefix!);

                        // 缓存内容
                        this.remoteCacheMap.set(virtualFile.path, {
                            content: transformedContent,
                            timestamp: Date.now()
                        });

                        // 创建并缓存元数据
                        await this.createAndCacheMetadata(virtualFile, transformedContent);

                        return transformedContent;
                    }

                    // 如果直接读取失败，回退到 API 方式
                    // console.log(`[Connect Vault] 直接读取失败，回退到 API 方式`);

                    const apiUrl = buildApiUrl(`http://127.0.0.1:${connection.port}`, filename, anchor);
                    // console.log(`[Connect Vault] API 请求: ${apiUrl}`);

                    const response = await fetch(apiUrl);
                    if (!response.ok) {
                        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();
                    let content = data.content || '';

                    // 转换内容中的内部链接
                    content = transformInternalLinks(content, virtualFile.crossVaultPrefix!);

                    // 缓存内容
                    this.remoteCacheMap.set(virtualFile.path, {
                        content,
                        timestamp: Date.now()
                    });

                    // 创建并缓存元数据
                    await this.createAndCacheMetadata(virtualFile, content);

                    return content;
                } catch (error) {
                    console.error(`[Connect Vault] 读取跨库文件失败:`, error);
                    return `# 错误\n\n无法读取跨库文件: ${virtualFile.path}\n\n错误信息: ${error.message}`;
                }
            }

            // console.log(`[Connect Vault] cachedRead 被调用: ${file.path}`);
            return originalCachedRead(file as TFile);
        };
    }

    /**
     * 拦截 exists 方法
     */
    private registerExistsPatch(): void {
        const originalExists = this.plugin.app.vault.adapter.exists.bind(this.plugin.app.vault.adapter);
        this.originalMethods.set('app.vault.adapter.exists', originalExists);

        (this.plugin.app.vault.adapter as any).exists = async (path: string): Promise<boolean> => {
            if (path.includes(':')) {
                // console.log(`[Connect Vault] exists 检查跨库文件: ${path}`);
                return true; // 跨库文件总是"存在"
            }
            return originalExists(path);
        };
    }

    /**
     * 拦截 getResourcePath 方法
     */
    private registerGetResourcePathPatch(): void {
        const originalGetResourcePath = this.plugin.app.vault.getResourcePath.bind(this.plugin.app.vault);
        this.originalMethods.set('app.vault.getResourcePath', originalGetResourcePath);

        (this.plugin.app.vault as any).getResourcePath = (file: TFile | VirtualFile): string => {
            const virtualFile = file as VirtualFile;

            if (virtualFile.isCrossVault) {
                const connection = virtualFile.crossVaultConnection;
                const resourceUrl = `http://127.0.0.1:${connection.port}/api/resource?name=${encodeURIComponent(virtualFile.basename)}`;
                // console.log(`[Connect Vault] 生成跨库资源路径: ${resourceUrl}`);
                return resourceUrl;
            }

            return originalGetResourcePath(file as TFile);
        };
    }

    /**
     * 拦截 modify 方法
     */
    private registerModifyPatch(): void {
        const originalModify = this.plugin.app.vault.modify.bind(this.plugin.app.vault);
        this.originalMethods.set('app.vault.modify', originalModify);

        (this.plugin.app.vault as any).modify = async (file: TFile | VirtualFile, data: string): Promise<void> => {
            const virtualFile = file as VirtualFile;

            if (virtualFile.isCrossVault) {
                // console.log(`[Connect Vault] 修改跨库文件: ${virtualFile.path}`);
                // console.log(`[Connect Vault] 锚点信息: ${virtualFile.crossVaultAnchor}`);

                const connection = virtualFile.crossVaultConnection;
                const filename = virtualFile.basename;
                const anchor = virtualFile.crossVaultAnchor;

                try {
                    // 构建 API URL，根据锚点类型添加相应参数
                    let apiUrl = `http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}&edit=true`;

                    if (anchor) {
                        // 判断锚点类型
                        if (anchor.startsWith('^')) {
                            // 块引用
                            const blockId = anchor.substring(1);
                            apiUrl += `&id=${encodeURIComponent(blockId)}`;
                            console.log(`[Connect Vault] 编辑块: ${blockId}`);
                        } else {
                            // 章节引用
                            apiUrl += `&head=${encodeURIComponent(anchor)}`;
                            console.log(`[Connect Vault] 编辑章节: ${anchor}`);
                        }
                    } else {
                        console.log(`[Connect Vault] 编辑整个文件`);
                    }

                    console.log(`[Connect Vault] 编辑请求 URL: ${apiUrl}`);

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            content: data
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`保存失败: ${response.status} ${response.statusText}`);
                    }

                    // 更新缓存
                    this.remoteCacheMap.set(virtualFile.path, {
                        content: data,
                        timestamp: Date.now()
                    });

                    console.log(`[Connect Vault] 跨库文件保存成功: ${virtualFile.path}`);
                } catch (error) {
                    console.error(`[Connect Vault] 保存跨库文件失败:`, error);
                    throw error;
                }
                return;
            }

            return originalModify(file as TFile, data);
        };
    }

    /**
     * 拦截 openLinkText 方法
     */
    private registerOpenLinkTextPatch(): void {
        const originalOpenLinkText = (this.plugin.app as any).workspace.openLinkText;
        if (!originalOpenLinkText) return;

        this.originalMethods.set('app.workspace.openLinkText', originalOpenLinkText);

        (this.plugin.app as any).workspace.openLinkText = (linktext: string, sourcePath: string, newLeaf?: boolean, openViewState?: any) => {
            const parsed = parseCrossVaultLink(linktext);
            if (parsed) {
                const connection = this.settings.connections.find(conn => conn.description === parsed.prefix);
                if (connection) {
                    const uri = generateObsidianUri(connection.vaultName, parsed.filename, parsed.anchor);
                    console.log(`[Connect Vault] 跳转到跨库链接: ${uri}`);
                    window.open(uri);
                    return;
                }
            }

            return originalOpenLinkText.call(this.plugin.app.workspace, linktext, sourcePath, newLeaf, openViewState);
        };
    }

    /**
     * 从当前悬停的 DOM 元素直接获取完整链接
     */
    private findFullLinkFromCurrentHover(partialLink: string): string {
        try {
            // console.log(`🔍 [Connect Vault] 开始从当前悬停元素查找完整链接，参数: "${partialLink}"`);

            // 1. 查找当前悬停的链接元素
            const hoveredElements = document.querySelectorAll('.cm-hmd-internal-link:hover, .internal-link:hover');
            // console.log(`🔍 [Connect Vault] 找到 ${hoveredElements.length} 个悬停的链接元素`);

            for (let i = 0; i < hoveredElements.length; i++) {
                const element = hoveredElements[i];
                const linkText = element.textContent;
                // console.log(`🔍 [Connect Vault] 检查悬停元素文本: "${linkText}"`);

                // 2. 尝试从前缀映射重构完整链接
                if (linkText && window.crossVaultPrefixMap) {
                    // 直接查找匹配
                    const fullLink = window.crossVaultPrefixMap.get(linkText);
                    if (fullLink && fullLink.includes(partialLink.split(':')[1] || '')) {
                        // console.log(`🎯 [Connect Vault] 从前缀映射找到匹配: "${fullLink}"`);
                        return fullLink;
                    }

                    // 查找包含该文件名的链接
                    for (const [filename, fullLinkText] of window.crossVaultPrefixMap.entries()) {
                        if (filename === linkText && fullLinkText.startsWith(partialLink.split(':')[0] + ':')) {
                            // console.log(`🎯 [Connect Vault] 从前缀映射找到精确匹配: "${fullLinkText}"`);
                            return fullLinkText;
                        }
                    }
                }

                // 3. 尝试从周围元素重构
                const reconstructed = this.reconstructFromSurroundingElements(element as HTMLElement, partialLink);
                if (reconstructed) {
                    // console.log(`🎯 [Connect Vault] 从周围元素重构: "${reconstructed}"`);
                    return reconstructed;
                }
            }

            // 4. 如果没有找到悬停元素，使用原来的方法
            // console.log(`🔍 [Connect Vault] 未找到悬停元素，使用原来的查找方法`);
            return this.findFullLinkInDocument(partialLink);

        } catch (e) {
            console.log('[Connect Vault] 从悬停元素查找失败:', e.message);
            return this.findFullLinkInDocument(partialLink);
        }
    }

    /**
     * 从周围元素重构完整链接
     */
    private reconstructFromSurroundingElements(element: HTMLElement, partialLink: string): string {
        const linkText = element.textContent || '';

        // 查找前面的兄弟元素中的前缀
        let currentElement: Node | null = element.previousSibling;
        while (currentElement) {
            const text = currentElement.textContent || '';
            // 查找类似 "test:" 的前缀
            const prefixMatch = text.match(/([^:\s\[\]]+):$/);
            if (prefixMatch && partialLink.startsWith(prefixMatch[1] + ':')) {
                return `${prefixMatch[1]}:${linkText}`;
            }
            currentElement = currentElement.previousSibling;
        }

        // 查找整行内容
        const lineElement = element.closest('.cm-line');
        if (lineElement) {
            const lineText = lineElement.textContent || '';
            // 查找包含当前链接的完整跨库链接格式
            const linkRegex = /\[\[([^:\]]+:[^\]]*)\]\]/g;
            let match;
            while ((match = linkRegex.exec(lineText)) !== null) {
                const fullLink = match[1];
                if (fullLink.includes(linkText) && fullLink.startsWith(partialLink.split(':')[0] + ':')) {
                    return fullLink;
                }
            }
        }

        return '';
    }

    /**
     * 在当前文档中查找包含指定前缀的完整链接
     * 优先使用前缀映射，回退到其他方法
     */
    private findFullLinkInDocument(partialLink: string): string {
        try {
            // console.log(`🚀🚀🚀 [Connect Vault] findFullLinkInDocument 被调用，参数: "${partialLink}" 🚀🚀🚀`);

            // 1. 首先尝试从全局前缀映射获取
            if (window.crossVaultPrefixMap) {
                // console.log(`🔍 [Connect Vault] 检查前缀映射，映射大小: ${window.crossVaultPrefixMap.size}`);

                // 从 partialLink 中提取文件名部分
                // 例如: "test:aa" -> "aa"
                const partialFilename = partialLink.includes(':') ? partialLink.split(':')[1] : partialLink;
                // console.log(`🔍 [Connect Vault] 提取的文件名部分: "${partialFilename}"`);

                // 遍历映射，查找匹配的链接
                for (const [filename, fullLink] of window.crossVaultPrefixMap.entries()) {
                    // console.log(`🔍 [Connect Vault] 检查映射: "${filename}" -> "${fullLink}"`);

                    // 检查是否匹配
                    // 1. 完整链接以 partialLink 开头
                    // 2. 或者映射的 filename 以 partialFilename 开头
                    // 3. 或者完整链接包含 partialFilename
                    if (fullLink.startsWith(partialLink) ||
                        filename.startsWith(partialFilename) ||
                        (fullLink.includes(':') && fullLink.split(':')[1].startsWith(partialFilename))) {
                        // console.log(`🎯 [Connect Vault] 从前缀映射找到匹配: "${fullLink}"`);
                        return fullLink;
                    }
                }
                // console.log(`🔍 [Connect Vault] 前缀映射中未找到匹配`);
            }

            // 2. 尝试从鼠标悬停信息获取
            if (this.eventHandlerManager) {
                const hoveredLinkText = this.eventHandlerManager.getCurrentHoveredLinkText();
                // console.log(`🔍 [Connect Vault] 鼠标悬停信息: "${hoveredLinkText}"`);
                if (hoveredLinkText && hoveredLinkText.startsWith(partialLink) && hoveredLinkText.includes(':')) {
                    // console.log(`🎯 [Connect Vault] 使用鼠标悬停信息: "${hoveredLinkText}"`);
                    return hoveredLinkText;
                }
            }

            const activeView = this.plugin.app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView || !(activeView as any).editor) {
                return '';
            }

            const editor = (activeView as any).editor;
            const content = editor.getValue();
            const cursor = editor.getCursor();

            // console.log(`🔍 [Connect Vault] 鼠标悬停信息不可用，在文档中搜索包含 "${partialLink}" 的完整链接`);
            // console.log(`📍 [Connect Vault] 当前光标位置: 第${cursor.line + 1}行, 第${cursor.ch + 1}列`);

            // 获取当前行内容
            const currentLine = editor.getLine(cursor.line);
            // console.log(`📝 [Connect Vault] 当前行内容: "${currentLine}"`);

            // 首先在当前行查找跨库链接
            const linkRegex = /\[\[([^\]]+)\]\]/g;
            let match;
            const lineLinks = [];

            // 重置正则表达式
            linkRegex.lastIndex = 0;
            while ((match = linkRegex.exec(currentLine)) !== null) {
                const linkText = match[1];
                if (linkText.startsWith(partialLink) && linkText.includes(':')) {
                    const linkStart = match.index;
                    const linkEnd = match.index + match[0].length;

                    lineLinks.push({
                        text: linkText,
                        start: linkStart,
                        end: linkEnd,
                        fullMatch: match[0]
                    });

                    console.log(`🔗 [Connect Vault] 当前行发现跨库链接: ${match[0]} (位置: ${linkStart}-${linkEnd})`);
                }
            }

            // 如果当前行有链接，检查光标是否在某个链接范围内
            if (lineLinks.length > 0) {
                for (const link of lineLinks) {
                    // 检查光标是否在链接范围内（给一些容错空间）
                    if (cursor.ch >= link.start - 2 && cursor.ch <= link.end + 2) {
                        console.log(`🎯 [Connect Vault] 光标在链接 ${link.fullMatch} 范围内，选择此链接`);
                        return link.text;
                    }
                }

                // 如果光标不在任何链接范围内，选择最接近的链接
                let closestLink = lineLinks[0];
                let minDistance = Math.abs(cursor.ch - (closestLink.start + closestLink.end) / 2);

                for (const link of lineLinks) {
                    const linkCenter = (link.start + link.end) / 2;
                    const distance = Math.abs(cursor.ch - linkCenter);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestLink = link;
                    }
                }

                console.log(`🎯 [Connect Vault] 选择最接近光标的链接: ${closestLink.fullMatch}`);
                return closestLink.text;
            }

            // 如果当前行没有找到，在整个文档中搜索
            // console.log(`🔍 [Connect Vault] 当前行未找到链接，在整个文档中搜索...`);

            const candidates = [];
            linkRegex.lastIndex = 0; // 重置正则表达式
            while ((match = linkRegex.exec(content)) !== null) {
                const linkText = match[1];
                if (linkText.startsWith(partialLink) && linkText.includes(':')) {
                    candidates.push(linkText);
                    console.log(`🔗 [Connect Vault] 找到候选链接: [[${linkText}]]`);
                }
            }

            if (candidates.length === 0) {
                console.log(`❌ [Connect Vault] 未找到匹配的跨库链接`);
                return '';
            }

            if (candidates.length === 1) {
                console.log(`✅ [Connect Vault] 找到唯一匹配: [[${candidates[0]}]]`);
                return candidates[0];
            }

            // 如果有多个候选，返回第一个找到的（通常是最相关的）
            console.log(`⚠️ [Connect Vault] 找到多个候选链接，返回第一个: [[${candidates[0]}]]`);
            console.log(`💡 [Connect Vault] 所有候选: ${candidates.map(c => `[[${c}]]`).join(', ')}`);
            return candidates[0];

        } catch (e) {
            console.log('[Connect Vault] 搜索完整链接失败:', e.message);
            return '';
        }
    }
}
