import MyPlugin from '../main';

export function registerMonkeyPatches(plugin: MyPlugin) {
    // 保存原始方法
    const originalCachedRead = plugin.app.vault.cachedRead;
    const originalGetResourcePath = plugin.app.vault.getResourcePath;

    // 拦截 cachedRead 方法
    plugin.register(() => {
        plugin.app.vault.cachedRead = originalCachedRead;
    });

    plugin.app.vault.cachedRead = async (file: any) => {
        // 获取文件路径
        const path = typeof file === 'string' ? file : file.path;

        // 检查路径是否符合我们的格式 "前置内容:文件名"
        const match = path.match(/^(.*?):(.*?)$/);
        if (match) {
            const [_, prefix, filename] = match;
            const connection = plugin.settings.connections.find(conn =>
                conn.description === prefix
            );

            if (connection) {
                try {
                    // 从远程库获取内容
                    const response = await fetch(`http://127.0.0.1:${connection.port}/api/note?name=${encodeURIComponent(filename)}`);
                    if (!response.ok) throw new Error('获取文件内容失败');

                    const data = await response.json();
                    return data.content;
                } catch (error) {
                    console.error('获取远程文件内容失败:', error);
                    throw new Error(`无法从远程库 ${prefix} 获取文件 ${filename}: ${error.message}`);
                }
            }
        }

        // 如果不符合我们的格式或没有找到对应的连接，使用原始方法
        return originalCachedRead.call(plugin.app.vault, file);
    };

    // 拦截 getResourcePath 方法
    plugin.register(() => {
        plugin.app.vault.getResourcePath = originalGetResourcePath;
    });
    
    plugin.app.vault.getResourcePath = (file: any) => {
        // 检查文件路径是否符合我们的格式
        if (typeof file === 'string') {
            const match = file.match(/^(.*?):(.*?)$/);
            if (match) {
                const [_, prefix, filename] = match;
                const connection = plugin.settings.connections.find(conn => 
                    conn.description === prefix
                );
                
                if (connection) {
                    // 构建远程资源路径
                    return `http://127.0.0.1:${connection.port}/api/resource?name=${encodeURIComponent(filename)}`;
                }
            }
        }
        
        // 如果不符合我们的格式或没有找到对应的连接，使用原始方法
        return originalGetResourcePath.call(plugin.app.vault, file);
    };
}