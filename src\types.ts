export interface CrossVaultLinkInfo {
    prefix: string;
    filename: string;
    anchor: string;
    anchorType: 'heading' | 'block' | null;
}

export interface VirtualFile {
    path: string;
    basename: string;
    extension: string;
    name: string;
    stat: {
        ctime: number;
        mtime: number;
        size: number;
    };
    vault: any;
    parent: any;
    // 添加 Obsidian 期望的属性
    deleted?: boolean;
    unsafeCachedData?: string;
    // 添加跨库链接特有的属性
    isCrossVault?: boolean;
    crossVaultPrefix?: string;
    crossVaultAnchor?: string;
    crossVaultConnection?: any;
}

export interface RemoteCacheEntry {
    content: string;
    timestamp: number;
    etag?: string;
}
