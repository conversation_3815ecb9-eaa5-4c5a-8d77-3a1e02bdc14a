# 智能前缀隐藏功能

Connect Vault 插件现在支持智能前缀隐藏功能，让跨库链接在不同情况下显示不同的内容，提供更清洁的阅读体验。

## 功能概述

### 🎯 核心特性

- **自动隐藏前缀**：在正常情况下隐藏 `库前缀:` 部分，只显示文件名
- **智能显示**：当光标在链接附近或鼠标悬停时，自动显示完整的前缀
- **模式适配**：在编辑模式和阅读模式下都有相应的优化显示

### 📱 显示效果

#### 正常状态
```
查看 [项目计划] 和 [会议记录]
```

#### 光标在附近或悬停时
```
查看 [work:项目计划] 和 [team:会议记录]
```

## 工作模式

### 1. 编辑模式（Edit Mode）

**正常状态**：
- 前缀部分透明度降低（30% 不透明度）
- 文件名部分正常显示
- 整体保持链接样式

**激活状态**（光标在当前行）：
- 前缀部分完全显示（100% 不透明度）
- 便于编辑和确认链接格式

**悬停状态**：
- 鼠标悬停时前缀完全显示
- 提供即时的完整信息

### 2. 阅读模式（Reading Mode）

**正常状态**：
- 前缀部分完全隐藏（0% 不透明度）
- 只显示文件名，提供清洁的阅读体验

**悬停状态**：
- 鼠标悬停时显示完整前缀
- 帮助用户了解链接的来源库

### 3. 实时预览模式（Live Preview）

- 结合编辑模式和阅读模式的特点
- 在非编辑状态下类似阅读模式
- 在编辑状态下类似编辑模式

## 技术实现

### 借鉴优秀插件的设计思路

本功能的实现借鉴了 Obsidian 社区中优秀插件的设计模式：

1. **Markdown 后处理器模式**：用于处理阅读模式
2. **DOM 操作优化**：避免复杂的 CodeMirror 装饰系统
3. **响应式样式设计**：使用 CSS 过渡动画

### CSS 样式控制

```css
/* 阅读模式样式 */
.cross-vault-prefix {
    color: var(--text-muted) !important;
    font-weight: normal !important;
    transition: opacity 0.2s ease-in-out;
}

.cross-vault-filename {
    color: var(--text-accent) !important;
    font-weight: inherit;
}

/* 编辑模式样式 */
.cross-vault-editor-prefix {
    color: var(--text-muted) !important;
    font-weight: normal !important;
    transition: opacity 0.2s ease-in-out;
}

.cross-vault-editor-filename {
    color: var(--text-accent) !important;
    font-weight: inherit;
}
```

### JavaScript 逻辑

1. **分离处理模式**：
   - 阅读模式：使用后处理器模式处理已渲染的链接
   - 编辑模式：使用 DOM 监听处理动态链接

2. **智能检测**：
   - 跨库链接识别：`prefix:filename` 格式匹配
   - 连接配置验证：确保前缀对应有效连接
   - 重复处理防护：避免多次处理同一链接

3. **交互响应**：
   - 光标检测：监听 `cm-active` 类变化
   - 鼠标事件：mouseenter/mouseleave 处理
   - 焦点事件：focus/blur 状态管理

## 使用示例

### 创建跨库链接

```markdown
# 项目文档

## 相关资料
- [[work:需求分析]]
- [[design:UI设计稿]]
- [[dev:技术方案]]

## 参考文档
- [[archive:类似项目经验]]
- [[team:最佳实践指南]]
```

### 显示效果对比

#### 传统显示（始终显示前缀）
```
- work:需求分析
- design:UI设计稿  
- dev:技术方案
- archive:类似项目经验
- team:最佳实践指南
```

#### 智能隐藏（正常状态）
```
- 需求分析
- UI设计稿
- 技术方案
- 类似项目经验
- 最佳实践指南
```

#### 智能显示（光标在附近或悬停）
```
- work:需求分析
- design:UI设计稿
- dev:技术方案
- archive:类似项目经验
- team:最佳实践指南
```

## 配置选项

目前智能前缀隐藏功能是自动启用的，未来版本可能会添加以下配置选项：

### 计划中的设置

- **启用/禁用智能隐藏**：全局开关
- **隐藏模式选择**：
  - 完全隐藏
  - 半透明显示
  - 自定义透明度
- **触发条件设置**：
  - 仅光标触发
  - 仅悬停触发
  - 光标+悬停触发
- **动画效果**：
  - 渐变时间设置
  - 动画类型选择

## 兼容性

### 支持的模式
- ✅ 编辑模式（Edit Mode）
- ✅ 阅读模式（Reading Mode）
- ✅ 实时预览模式（Live Preview）

### 支持的链接类型
- ✅ 普通跨库链接 `[[prefix:filename]]`
- ✅ 带显示文本的链接 `[[prefix:filename|显示文本]]`
- ⚠️ 嵌入链接 `![[prefix:filename]]`（部分支持）

### 浏览器兼容性
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## 故障排除

### 常见问题

1. **前缀不隐藏**
   - 检查链接格式是否正确
   - 确认连接配置中有对应的前缀
   - 查看控制台是否有错误信息

2. **悬停不显示前缀**
   - 确认鼠标确实悬停在链接上
   - 检查 CSS 样式是否被其他插件覆盖

3. **光标检测不工作**
   - 确认在编辑模式下
   - 检查是否有其他插件干扰

### 调试方法

1. **检查 DOM 结构**：
   ```javascript
   // 在控制台中检查链接结构
   document.querySelectorAll('.cm-hmd-internal-link').forEach(link => {
       console.log(link.innerHTML);
   });
   ```

2. **检查样式应用**：
   ```javascript
   // 检查样式是否正确应用
   document.querySelectorAll('.cross-vault-prefix').forEach(prefix => {
       console.log(getComputedStyle(prefix).opacity);
   });
   ```

## 最佳实践

### 1. 前缀命名
- 使用简短、有意义的前缀
- 避免过长的前缀影响阅读体验
- 建议使用 2-6 个字符

### 2. 文件命名
- 使用清晰、描述性的文件名
- 即使隐藏前缀也能理解文件内容
- 避免依赖前缀来理解文件用途

### 3. 使用场景
- 在需要清洁阅读体验的文档中启用
- 在需要明确库来源的技术文档中谨慎使用
- 根据团队习惯调整使用方式

## 更新日志

### v1.0.0
- ✨ 新增智能前缀隐藏功能
- ✨ 支持编辑模式和阅读模式
- ✨ 添加光标检测和悬停效果
- ✨ 平滑的透明度过渡动画

## 反馈与建议

如果您对智能前缀隐藏功能有任何建议或遇到问题，欢迎：

1. 在 GitHub 上提交 Issue
2. 提供具体的使用场景和期望效果
3. 分享您的配置和使用经验

我们会根据用户反馈持续改进这个功能。
